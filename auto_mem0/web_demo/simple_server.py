#!/usr/bin/env python3
import sys
import os
import json
import asyncio
import logging
from datetime import datetime

# 配置专用的logger，避免影响其他库的logging
def setup_loggers():
    """设置我们需要的logger"""
    # 创建共享的formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s:%(lineno)d - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 创建共享的handler
    handler = logging.StreamHandler()
    handler.setFormatter(formatter)

    # 配置simple_server.py的logger
    simple_logger = logging.getLogger('simple_server.py')
    if not simple_logger.handlers:
        simple_logger.addHandler(handler)
        simple_logger.setLevel(logging.INFO)
        simple_logger.propagate = False

    # 配置enhanced_memory.py的logger
    enhanced_logger = logging.getLogger('enhanced_memory.py')
    if not enhanced_logger.handlers:
        enhanced_logger.addHandler(handler)
        enhanced_logger.setLevel(logging.INFO)
        enhanced_logger.propagate = False

    return simple_logger

logger = setup_loggers()

# 添加路径
sys.path.append(".")
sys.path.append("..")

from fastapi import FastAPI, Request
from fastapi.responses import FileResponse, JSONResponse, StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles

# 尝试导入 EnhancedMemory
try:
    from enhanced_memory import EnhancedMemory, create_classifier_function, ExtractedMemory
    ENHANCED_MEMORY_AVAILABLE = True
    logger.info("✅ EnhancedMemory 可用")
except Exception as e:
    logger.warning(f"⚠️ EnhancedMemory 不可用: {e}")
    ENHANCED_MEMORY_AVAILABLE = False

# 配置路径
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
STATIC_DIR = os.path.join(BASE_DIR, 'static')
os.makedirs(STATIC_DIR, exist_ok=True)

# 模拟数据存储
CHAT_HISTORIES = {}
MOCK_MEMORIES = []
SSE_CONSUMERS = {}

# 初始化 EnhancedMemory（如果可用）
MEMORY = None
CLASSIFIER = None

def filter_think_tags(text):
    """移除文本中的 <think>...</think> 标签及其内容"""
    import re
    return re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL | re.IGNORECASE).strip()

def init_enhanced_memory():
    global MEMORY, CLASSIFIER
    if not ENHANCED_MEMORY_AVAILABLE:
        return False

    try:
        config = {
            "vector_store": {
                "provider": "chroma",
                "config": {"collection_name": "web_demo_05", "path": "./db/chroma_db"},
            },
            # "llm": {
            #     "provider": "ollama",
            #     "config": {
            #         "model": "qwen3:8b",
            #         "temperature": 0.1,
            #         "ollama_base_url": "http://************:8088",
            #     },
            # },
            "llm": {
                "provider": "openai",
                "config": {
                    "model": "SenseAuto-Chat",
                    "temperature": 0.1,
                    "api_key": "1s3963nw8802M4O55yMuU6x37tOYQ682",
                    "openai_base_url": "http://************:8099/v1",
                },
            },
            "embedder": {
                "provider": "ollama",
                "config": {"model": "qwen3:8b", "ollama_base_url": "http://************:8088"},
            },
            "version": "v1.1",
        }

        MEMORY = EnhancedMemory.from_config(config)

        # 尝试加载分类器
        categories_path = "../memory_categories.json"
        if os.path.exists(categories_path):
            with open(categories_path, "r", encoding="utf-8") as f:
                categories_config = json.load(f)
            CLASSIFIER = create_classifier_function(
                categories_config,
                default_category="general",
                llm_config=MEMORY.config.llm.config,
            )

        logger.info("✅ EnhancedMemory 初始化成功")
        return True
    except Exception as e:
        logger.warning(f"⚠️ EnhancedMemory 初始化失败: {e}")
        return False

logger.info("创建 FastAPI 应用...")
app = FastAPI(title="Mem0 Web Demo 05 - Simple", version="0.1.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件
app.mount("/static", StaticFiles(directory=STATIC_DIR), name="static")

@app.get("/")
async def index():
    return FileResponse(os.path.join(STATIC_DIR, "index.html"))

@app.get("/api/test")
async def test():
    return {"status": "ok", "message": "简化版服务器运行正常"}

# SSE 事件流
@app.get("/events")
async def sse_events(request: Request, user_id: str):
    """SSE stream endpoint per user."""
    q = asyncio.Queue()
    SSE_CONSUMERS.setdefault(user_id, []).append(q)

    async def event_generator():
        try:
            # 发送连接成功消息
            await q.put({
                "event": "log",
                "data": json.dumps({
                    "level": "INFO",
                    "message": "SSE 连接成功",
                    "timestamp": datetime.utcnow().isoformat() + "Z",
                }, ensure_ascii=False),
            })

            while True:
                if await request.is_disconnected():
                    break
                try:
                    msg = await asyncio.wait_for(q.get(), timeout=15.0)
                except asyncio.TimeoutError:
                    # 心跳
                    yield b": ping\n\n"
                else:
                    yield f"event: {msg['event']}\n".encode("utf-8")
                    yield f"data: {msg['data']}\n\n".encode("utf-8")
        finally:
            # 清理
            consumers = SSE_CONSUMERS.get(user_id, [])
            if q in consumers:
                consumers.remove(q)

    return StreamingResponse(event_generator(), media_type="text/event-stream")

@app.get("/api/memories")
async def list_memories(user_id: str, limit: int = 200):
    if MEMORY:
        try:
            # 使用真正的 EnhancedMemory 获取记忆
            result = MEMORY.get_all(user_id=user_id, limit=limit) or {"results": []}
            logger.info(f"获取到 {len(result.get('results', []))} 条记忆")
            return result
        except Exception as e:
            logger.error(f"获取记忆失败: {e}")
            # 回退到模拟数据
            user_memories = [m for m in MOCK_MEMORIES if m.get("user_id") == user_id]
            return {"results": user_memories}
    else:
        # 使用模拟的记忆数据
        user_memories = [m for m in MOCK_MEMORIES if m.get("user_id") == user_id]
        return {"results": user_memories}

@app.delete("/api/memories")
async def clear_memories(user_id: str):
    if MEMORY:
        try:
            MEMORY.delete_all(user_id=user_id)
            logger.info(f"清空了用户 {user_id} 的所有记忆")
            return {"ok": True}
        except Exception as e:
            logger.error(f"清空记忆失败: {e}")
            return {"ok": False, "error": str(e)}
    else:
        # 回退到模拟数据
        global MOCK_MEMORIES
        MOCK_MEMORIES = [m for m in MOCK_MEMORIES if m.get("user_id") != user_id]
        return {"ok": True}

@app.post("/api/memories/delete")
async def delete_memory(body: dict):
    memory_id = body.get("memory_id")
    if not memory_id:
        return {"ok": False, "error": "memory_id required"}

    if MEMORY:
        try:
            MEMORY.delete(memory_id)
            logger.info(f"删除了记忆 {memory_id}")
            return {"ok": True}
        except Exception as e:
            logger.error(f"删除记忆失败: {e}")
            return {"ok": False, "error": str(e)}
    else:
        # 回退到模拟数据
        global MOCK_MEMORIES
        MOCK_MEMORIES = [m for m in MOCK_MEMORIES if m.get("id") != memory_id]
        return {"ok": True}

@app.post("/api/chat")
async def chat(body: dict):
    user_id = body.get("user_id", "demo_user")
    message = body.get("message", "")
    extract_memory = body.get("extract_memory", True)  # 默认提取记忆

    # 更新聊天历史
    history = CHAT_HISTORIES.setdefault(user_id, [])
    history.append({"role": "user", "content": message})

    stored_memories = []

    # 只有在需要提取记忆时才进行记忆处理
    if extract_memory and MEMORY and CLASSIFIER:
        try:
            # 提取记忆
            extracted = MEMORY.extract_memories([{"role": "user", "content": message}], user_id=user_id)
            classified = []
            for mem in extracted:
                # UPDATE事件和NONE事件跳过分类
                if hasattr(mem, 'event') and mem.event == "UPDATE":
                    logger.info(f"🔄 UPDATE事件跳过分类: {mem.text}")
                    classified.append(mem)
                elif hasattr(mem, 'event') and mem.event == "NONE":
                    logger.info(f"⏭️ NONE事件跳过处理: {mem.text}")
                    # NONE事件什么都不做，不添加到classified列表
                else:
                    # 其他事件进行分类
                    try:
                        category, metadata = CLASSIFIER(mem.text)
                        mem.set_category(category)
                        mem.set_metadata(metadata)
                        classified.append(mem)
                    except Exception as ce:
                        logger.warning(f"分类失败: {ce}, 原记忆: {mem.text}")

            if classified:
                store_result = MEMORY.store_memories(classified, user_id=user_id, base_metadata={"user_id": user_id})
                stored_memories = store_result.get("results", [])
                logger.info(f"存储了 {len(stored_memories)} 条记忆")
        except Exception as e:
            logger.error(f"记忆处理失败: {e}")
    elif extract_memory:
        # 回退到模拟记忆存储（只有在需要提取记忆时）
        if len(message) > 10:
            memory = {
                "id": f"mem_{len(MOCK_MEMORIES) + 1}",
                "memory": f"用户说：{message}",
                "user_id": user_id,
                "metadata": {"category": "general"},
                "created_at": datetime.utcnow().isoformat() + "Z"
            }
            MOCK_MEMORIES.append(memory)
            stored_memories = [memory]

    # 生成回复
    if MEMORY:
        try:
            # 使用真正的 LLM 生成回复
            raw_reply = MEMORY.llm.generate_response(messages=history + [{"role": "system", "content": "回答使用中文。"}])
            # 过滤掉 <think> 标签内容
            reply = filter_think_tags(raw_reply)
        except Exception as e:
            logger.error(f"LLM 生成失败: {e}")
            reply = f"抱歉，LLM 服务暂时不可用。你的消息是：{message}"
    else:
        # 回退到模拟回复
        reply = f"我收到了你的消息：{message}。这是一个简化版的演示。"

    history.append({"role": "assistant", "content": reply})

    return {
        "reply": reply,
        "stored": stored_memories,
        "history_len": len(history)
    }

@app.post("/api/chat/stream")
async def chat_stream(body: dict):
    user_id = body.get("user_id", "demo_user")
    message = body.get("message", "")
    extract_memory = body.get("extract_memory", True)

    # 更新聊天历史
    history = CHAT_HISTORIES.setdefault(user_id, [])
    history.append({"role": "user", "content": message})

    async def generate_stream():
        stored_memories = []

        # 只有在需要提取记忆时才进行记忆处理
        if extract_memory and MEMORY and CLASSIFIER:
            try:
                # 提取记忆
                extracted = MEMORY.extract_memories([{"role": "user", "content": message}], user_id=user_id)
                classified = []
                for mem in extracted:
                    # UPDATE事件和NONE事件跳过分类
                    if hasattr(mem, 'event') and mem.event == "UPDATE":
                        logger.info(f"🔄 UPDATE事件跳过分类: {mem.text}")
                        classified.append(mem)
                    elif hasattr(mem, 'event') and mem.event == "NONE":
                        logger.info(f"⏭️ NONE事件跳过处理: {mem.text}")
                        # NONE事件什么都不做，不添加到classified列表
                    else:
                        # 其他事件进行分类
                        try:
                            category, metadata = CLASSIFIER(mem.text)
                            mem.set_category(category)
                            mem.set_metadata(metadata)
                            classified.append(mem)
                        except Exception as ce:
                            logger.warning(f"分类失败: {ce},--原记忆: {mem.text}")

                if classified:
                    store_result = MEMORY.store_memories(classified, user_id=user_id, base_metadata={"user_id": user_id})
                    stored_memories = store_result.get("results", [])
                    logger.info(f"存储了 {len(stored_memories)} 条记忆")
            except Exception as e:
                logger.error(f"记忆处理失败: {e} ")
        elif extract_memory:
            # 回退到模拟记忆存储（只有在需要提取记忆时）
            if len(message) > 10:
                memory = {
                    "id": f"mem_{len(MOCK_MEMORIES) + 1}",
                    "memory": f"用户说：{message}",
                    "user_id": user_id,
                    "metadata": {"category": "general"},
                    "created_at": datetime.utcnow().isoformat() + "Z"
                }
                MOCK_MEMORIES.append(memory)
                stored_memories = [memory]

        # 生成流式回复
        if MEMORY:
            try:
                # 使用真正的 LLM 生成流式回复
                raw_reply = MEMORY.llm.generate_response(messages=history + [{"role": "system", "content": "回答使用中文。"}])
                # 过滤掉 <think> 标签内容
                reply = filter_think_tags(raw_reply)

                # 模拟流式输出
                words = reply.split()
                for i, word in enumerate(words):
                    chunk = word + (" " if i < len(words) - 1 else "")
                    yield f"data: {json.dumps({'content': chunk}, ensure_ascii=False)}\n\n"
                    await asyncio.sleep(0.05)  # 模拟打字延迟

            except Exception as e:
                logger.error(f"LLM 生成失败: {e}")
                reply = f"抱歉，LLM 服务暂时不可用。你的消息是：{message}"
                yield f"data: {json.dumps({'content': reply}, ensure_ascii=False)}\n\n"
        else:
            # 回退到模拟回复
            reply = f"我收到了你的消息：{message}。这是一个简化版的演示。"
            # 模拟流式输出
            for char in reply:
                yield f"data: {json.dumps({'content': char}, ensure_ascii=False)}\n\n"
                await asyncio.sleep(0.03)

        # 添加回复到历史记录
        history.append({"role": "assistant", "content": reply if 'reply' in locals() else "回复生成失败"})

        # 发送完成信号
        yield "data: [DONE]\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
        }
    )

@app.post("/api/chat/reset")
async def reset_chat(body: dict):
    user_id = body.get("user_id", "demo_user")
    CHAT_HISTORIES[user_id] = []
    return {"ok": True}

@app.post("/api/extract-memories")
async def extract_memories_batch(body: dict):
    user_id = body.get("user_id", "demo_user")

    # 获取用户的聊天历史
    history = CHAT_HISTORIES.get(user_id, [])
    if not history:
        return {"extracted_count": 0, "message": "没有找到聊天历史"}

    extracted_count = 0

    if MEMORY and CLASSIFIER:
        try:
            # 批量提取记忆
            extracted = MEMORY.extract_memories(history, user_id=user_id)
            classified = []

            for mem in extracted:
                # UPDATE事件和NONE事件跳过分类
                if hasattr(mem, 'event') and mem.event == "UPDATE":
                    logger.info(f"🔄 UPDATE事件跳过分类: {mem.text}")
                    classified.append(mem)
                elif hasattr(mem, 'event') and mem.event == "NONE":
                    logger.info(f"⏭️ NONE事件跳过处理: {mem.text}")
                    # NONE事件什么都不做，不添加到classified列表
                else:
                    # 其他事件进行分类
                    try:
                        category, metadata = CLASSIFIER(mem.text)
                        mem.set_category(category)
                        mem.set_metadata(metadata)
                        classified.append(mem)
                    except Exception as ce:
                        logger.warning(f"分类失败: {ce} - 原记忆: {mem.text}")

            if classified:
                store_result = MEMORY.store_memories(classified, user_id=user_id, base_metadata={"user_id": user_id})
                extracted_count = len(store_result.get("results", []))
                logger.info(f"批量存储了 {extracted_count} 条记忆")

        except Exception as e:
            logger.error(f"批量记忆处理失败: {e}")
            return {"extracted_count": 0, "error": str(e)}
    else:
        # 模拟模式：从聊天历史中提取用户消息作为记忆
        global MOCK_MEMORIES
        for msg in history:
            if msg.get("role") == "user" and len(msg.get("content", "")) > 10:
                memory = {
                    "id": f"mem_{len(MOCK_MEMORIES) + 1}",
                    "memory": f"用户说：{msg['content']}",
                    "user_id": user_id,
                    "metadata": {"category": "general"},
                    "created_at": datetime.utcnow().isoformat() + "Z"
                }
                MOCK_MEMORIES.append(memory)
                extracted_count += 1

    return {"extracted_count": extracted_count, "message": f"成功提取了 {extracted_count} 条记忆"}

@app.get("/healthz")
async def health():
    return {"status": "ok"}

if __name__ == "__main__":
    logger.info("启动简化版服务器...")

    # 尝试初始化 EnhancedMemory
    if init_enhanced_memory():
        logger.info("🚀 使用真正的 LLM 和记忆系统")
    else:
        logger.warning("⚠️ 使用模拟模式（无 LLM 集成）")

    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8765, log_level="info")
