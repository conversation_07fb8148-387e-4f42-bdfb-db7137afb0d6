# Enhanced Memory System

基于Mem0的增强记忆系统，支持智能分类、事件处理和Web界面交互。

## 🚀 项目特性

### 核心功能
- **智能记忆分类**：使用LLM自动对记忆进行分类
- **事件驱动处理**：支持ADD、UPDATE、DELETE、NONE等事件类型
- **向量存储**：基于ChromaDB的高效向量存储和检索
- **Web界面**：提供简洁的Web界面进行记忆管理
- **实时日志**：详细的日志系统，显示具体行号信息

### 增强特性
- **分类优化**：UPDATE事件保持原有分类，避免重复分类
- **多模型支持**：支持OpenAI、Ollama等多种LLM和嵌入模型
- **灵活配置**：支持多种配置方式和自定义分类规则
- **批量处理**：支持批量记忆提取和处理

## 📁 项目结构

```
auto_mem0/
├── enhanced_memory.py          # 增强记忆核心模块
├── web_demo/
│   ├── simple_server.py       # Web服务器
│   ├── static/                # 静态文件
│   └── templates/             # HTML模板
├── db/                        # 数据库文件
├── config/                    # 配置文件
└── README.md                  # 项目文档
```

## 🛠️ 安装和配置

### 环境要求
- Python 3.8+
- 依赖包：mem0ai, fastapi, uvicorn, requests等

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd auto_mem0
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置模型**
编辑配置文件，设置LLM和嵌入模型：
```python
config = {
    "vector_store": {
        "provider": "chroma",
        "config": {
            "collection_name": "your_collection",
            "path": "./db/chroma_db"
        }
    },
    "llm": {
        "provider": "openai",
        "config": {
            "model": "your-model",
            "api_key": "your-api-key",
            "openai_base_url": "your-base-url"
        }
    },
    "embedder": {
        "provider": "ollama",
        "config": {
            "model": "your-embedding-model",
            "ollama_base_url": "your-ollama-url"
        }
    }
}
```

## 🚀 快速开始

### 启动Web服务器
```bash
cd web_demo
python3 simple_server.py
```

服务器将在 `http://localhost:8765` 启动

### 使用API

#### 1. 发送聊天消息并提取记忆
```bash
curl -X POST "http://localhost:8765/api/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user123",
    "message": "我叫张三，是一名软件工程师",
    "extract_memory": true
  }'
```

#### 2. 获取用户记忆
```bash
curl "http://localhost:8765/api/memories?user_id=user123"
```

#### 3. 删除记忆
```bash
curl -X DELETE "http://localhost:8765/api/memories/memory_id"
```

#### 4. 清空用户记忆
```bash
curl -X DELETE "http://localhost:8765/api/memories?user_id=user123"
```

### 使用Python API

```python
from enhanced_memory import EnhancedMemory, create_classifier_function

# 创建增强记忆实例
memory = EnhancedMemory.from_config(config)

# 定义分类规则
categories_config = {
    "personal_info": {
        "description": "个人基本信息，包括姓名、年龄、职业等",
        "examples": "我叫张三、今年25岁、是一名工程师"
    },
    "preferences": {
        "description": "个人偏好和喜好",
        "examples": "喜欢喝咖啡、不喜欢辣食"
    }
}

# 创建分类器
classifier = create_classifier_function(categories_config)

# 添加记忆并分类
messages = [{"role": "user", "content": "我叫李四，喜欢编程"}]
result = memory.add_with_classification(messages, classifier, user_id="user123")
```

## 📊 事件处理机制

系统支持多种事件类型，每种事件有不同的处理策略：

| 事件类型   | 处理方式              | 是否分类 | 是否存储 | 说明                       |
| ---------- | --------------------- | -------- | -------- | -------------------------- |
| **ADD**    | 分类 + 存储           | ✅        | ✅        | 新增记忆，完整分类流程     |
| **UPDATE** | 保持原分类 + 更新内容 | ❌        | ✅        | 更新记忆内容，保持原有分类 |
| **DELETE** | 直接删除              | ❌        | ❌        | 删除记忆，无需分类         |
| **NONE**   | 什么都不做            | ❌        | ❌        | 真正的无操作，不分类不存储 |
| **GET**    | 检索                  | ❌        | ❌        | 获取记忆，无需分类         |

### 特殊事件处理

#### UPDATE事件
- **不重新分类**：节省LLM调用成本
- **保持原有分类**：确保分类一致性
- **更新内容和向量**：记忆内容得到正确更新

#### NONE事件
- **完全跳过处理**：真正的无操作事件
- **不调用LLM分类**：节省计算资源和时间
- **不存储任何内容**：不会产生新的记忆或更新现有记忆
- **适用场景**：日常对话、确认回复等无需记忆的内容

## 🔧 配置说明

### 分类配置
```python
categories_config = {
    "category_name": {
        "description": "分类描述",
        "examples": "分类示例"
    }
}
```

### 日志配置
系统使用详细的日志记录，支持显示行号：
```python
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s:%(lineno)d - %(levelname)s - %(message)s'
)
```

## 🌐 Web界面

访问 `http://localhost:8765` 可以使用Web界面：
- 实时聊天和记忆提取
- 记忆列表查看和管理
- 记忆分类和搜索
- 批量记忆处理

## 📝 API文档

### 聊天API
- `POST /api/chat` - 发送消息并提取记忆
- `POST /api/chat-stream` - 流式聊天响应

### 记忆管理API
- `GET /api/memories` - 获取记忆列表
- `DELETE /api/memories/{memory_id}` - 删除特定记忆
- `DELETE /api/memories` - 清空用户记忆
- `POST /api/extract-memories` - 批量提取记忆

### 系统API
- `GET /api/test` - 系统状态检查
- `GET /events` - SSE事件流

## 🔍 日志和调试

系统提供详细的日志输出，包括：
- 记忆提取和分类过程
- 事件处理详情
- 错误和警告信息
- 性能统计

日志格式：`时间戳 - 模块名:行号 - 级别 - 消息`

## 🆘 常见问题

### Q: UPDATE事件为什么不重新分类？
A: 为了节省LLM调用成本和保持分类一致性，UPDATE事件只更新内容和向量，保持原有分类。

### Q: NONE事件为什么什么都不做？
A: NONE事件表示无需记忆的内容（如"好的"、"知道了"等确认回复），为了节省计算资源，系统会完全跳过处理，不进行分类也不存储。

### Q: 如何自定义分类规则？
A: 修改`categories_config`配置，定义新的分类类别和示例。

### Q: 支持哪些LLM模型？
A: 支持OpenAI API兼容的模型和Ollama本地模型。

### Q: 如何处理分类失败？
A: 系统会自动将分类失败的记忆归类为"general"类别，并记录错误日志。
