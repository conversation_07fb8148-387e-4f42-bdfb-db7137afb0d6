{"cells": [{"cell_type": "markdown", "metadata": {"id": "b02n_zJ_hl3d"}, "source": ["## Cookbook for using Anthropic with Embedchain\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "gyJ6ui2vhtMY"}, "source": ["### Step-1: Install embedchain package"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "-NbXjAdlh0vJ", "outputId": "efdce0dc-fb30-4e01-f5a8-ef1a7f4e8c09"}, "outputs": [], "source": ["!pip install embedchain"]}, {"cell_type": "markdown", "metadata": {"id": "nGnpSYAAh2bQ"}, "source": ["### Step-2: Set Anthropic related environment variables\n", "\n", "You can find `OPENAI_API_KEY` on your [OpenAI dashboard](https://platform.openai.com/account/api-keys) and `ANTHROPIC_API_KEY` on your [Anthropic dashboard](https://console.anthropic.com/account/keys)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "0fBdQ9GAiRvK"}, "outputs": [], "source": ["import os\n", "from embedchain import App\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-xxx\"\n", "os.environ[\"ANTHROPIC_API_KEY\"] = \"xxx\""]}, {"cell_type": "markdown", "metadata": {"id": "PGt6uPLIi1CS"}, "source": ["### Step-3: Create embedchain app and define your config"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Amzxk3m-i3tD"}, "outputs": [], "source": ["app = App.from_config(config={\n", "    \"provider\": \"anthropic\",\n", "    \"config\": {\n", "        \"model\": \"claude-instant-1\",\n", "        \"temperature\": 0.5,\n", "        \"top_p\": 1,\n", "        \"stream\": False\n", "    }\n", "})"]}, {"cell_type": "markdown", "metadata": {"id": "XNXv4yZwi7ef"}, "source": ["### Step-4: Add data sources to your app"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 52}, "id": "Sn_0rx9QjIY9", "outputId": "dc17baec-39b5-4dc8-bd42-f2aad92697eb"}, "outputs": [], "source": ["app.add(\"https://www.forbes.com/profile/elon-musk\")"]}, {"cell_type": "markdown", "metadata": {"id": "_7W6fDeAjMAP"}, "source": ["### Step-5: All set. Now start asking questions related to your data"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 391}, "id": "cvIK7dWRjN_f", "outputId": "3d1cb7ce-969e-4dad-d48c-b818b7447cc0"}, "outputs": [], "source": ["while(True):\n", "    question = input(\"Enter question: \")\n", "    if question in ['q', 'exit', 'quit']:\n", "        break\n", "    answer = app.query(question)\n", "    print(answer)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}