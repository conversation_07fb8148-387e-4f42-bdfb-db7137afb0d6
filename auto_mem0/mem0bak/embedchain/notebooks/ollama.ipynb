{"cells": [{"cell_type": "markdown", "metadata": {"id": "b02n_zJ_hl3d"}, "source": ["## Cookbook for using Ollama with Embedchain"]}, {"cell_type": "markdown", "metadata": {"id": "gyJ6ui2vhtMY"}, "source": ["### Step-1: <PERSON>up <PERSON>, follow these instructions https://github.com/jmorganca/ollama\n", "\n", "Once Setup is done:\n", "\n", "- ollama pull llama2 (All supported models can be found here: https://ollama.ai/library)\n", "- ollama run llama2 (Test out the model once)\n", "- ollama serve"]}, {"cell_type": "markdown", "metadata": {"id": "PGt6uPLIi1CS"}, "source": ["### Step-2 Create embedchain app and define your config (all local inference)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 321}, "id": "Amzxk3m-i3tD", "outputId": "afe8afde-5cb8-46bc-c541-3ad26cc3fa6e"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/workspace/embedchain/.venv/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from embedchain import App\n", "app = App.from_config(config={\n", "    \"llm\": {\n", "        \"provider\": \"ollama\",\n", "        \"config\": {\n", "            \"model\": \"llama2\",\n", "            \"temperature\": 0.5,\n", "            \"top_p\": 1,\n", "            \"stream\": True\n", "        }\n", "    },\n", "    \"embedder\": {\n", "        \"provider\": \"huggingface\",\n", "        \"config\": {\n", "            \"model\": \"BAAI/bge-small-en-v1.5\"\n", "        }\n", "    }\n", "})"]}, {"cell_type": "markdown", "metadata": {"id": "XNXv4yZwi7ef"}, "source": ["### Step-3: Add data sources to your app"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 176}, "id": "Sn_0rx9QjIY9", "outputId": "2f2718a4-3b7e-4844-fd46-3e0857653ca0"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Inserting batches in chromadb: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 1/1 [00:00<00:00,  1.57it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Successfully saved https://www.forbes.com/profile/elon-musk (DataType.WEB_PAGE). New chunks count: 4\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"data": {"text/plain": ["'8cf46026cabf9b05394a2658bd1fe890'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["app.add(\"https://www.forbes.com/profile/elon-musk\")"]}, {"cell_type": "markdown", "metadata": {"id": "_7W6fDeAjMAP"}, "source": ["### Step-4: All set. Now start asking questions related to your data"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cvIK7dWRjN_f", "outputId": "79e873c8-9594-45da-f5a3-0a893511267f"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON> is a business magnate, investor, and engineer. He is the CEO of SpaceX and Tesla, Inc., and has been involved in other successful ventures such as Neuralink and The Boring Company. <PERSON><PERSON> is known for his innovative ideas, entrepreneurial spirit, and vision for the future of humanity.\n", "\n", "As the CEO of Tesla, <PERSON><PERSON> has played a significant role in popularizing electric vehicles and making them more accessible to the masses. Under his leadership, Tesla has grown into one of the most valuable companies in the world.\n", "\n", "SpaceX, another company founded by <PERSON><PERSON>, is a leading player in the commercial space industry. SpaceX has developed advanced rockets and spacecraft, including the Falcon 9 and Dragon, which have successfully launched numerous satellites and other payloads into orbit.\n", "\n", "<PERSON><PERSON> is also known for his ambitious goals, such as establishing a human settlement on Mars and developing sustainable energy solutions to address climate change. He has been recognized for his philanthropic efforts, particularly in the area of education, and has been awarded numerous honors and awards for his contributions to society.\n", "\n", "Overall, <PERSON><PERSON> is a highly influential and innovative entrepreneur who has made significant impacts in various industries and has inspired many people around the world with his vision and leadership."]}], "source": ["answer = app.query(\"who is elon musk?\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 4}