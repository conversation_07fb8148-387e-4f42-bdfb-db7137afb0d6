{"cells": [{"cell_type": "markdown", "metadata": {"id": "b02n_zJ_hl3d"}, "source": ["## Cookbook for using JinaChat with Embedchain"]}, {"cell_type": "markdown", "metadata": {"id": "gyJ6ui2vhtMY"}, "source": ["### Step-1: Install embedchain package"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "-NbXjAdlh0vJ", "outputId": "69cb79a6-c758-4656-ccf7-9f3105c81d16"}, "outputs": [], "source": ["!pip install embedchain"]}, {"cell_type": "markdown", "metadata": {"id": "nGnpSYAAh2bQ"}, "source": ["### Step-2: <PERSON> related environment variables\n", "\n", "You can find `OPENAI_API_KEY` on your [OpenAI dashboard](https://platform.openai.com/account/api-keys) and `JINACHAT_API_KEY` key on your [Chat Jina dashboard](https://chat.jina.ai/api)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "0fBdQ9GAiRvK"}, "outputs": [], "source": ["import os\n", "from embedchain import App\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-xxx\"\n", "os.environ[\"JINACHAT_API_KEY\"] = \"xxx\""]}, {"cell_type": "markdown", "metadata": {"id": "PGt6uPLIi1CS"}, "source": ["### Step-3 Create embedchain app and define your config"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 321}, "id": "Amzxk3m-i3tD", "outputId": "8d00da74-5f73-49bb-b868-dcf1c375ac85"}, "outputs": [], "source": ["app = App.from_config(config={\n", "    \"provider\": \"jina\",\n", "    \"config\": {\n", "        \"temperature\": 0.5,\n", "        \"max_tokens\": 1000,\n", "        \"top_p\": 1,\n", "        \"stream\": False\n", "    }\n", "})"]}, {"cell_type": "markdown", "metadata": {"id": "XNXv4yZwi7ef"}, "source": ["### Step-4: Add data sources to your app"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 52}, "id": "Sn_0rx9QjIY9", "outputId": "10eeacc7-9263-448e-876d-002af897ebe5"}, "outputs": [], "source": ["app.add(\"https://www.forbes.com/profile/elon-musk\")"]}, {"cell_type": "markdown", "metadata": {"id": "_7W6fDeAjMAP"}, "source": ["### Step-5: All set. Now start asking questions related to your data"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cvIK7dWRjN_f", "outputId": "7dc7212f-a0e9-43c8-f119-f595ba79b4b7"}, "outputs": [], "source": ["while(True):\n", "    question = input(\"Enter question: \")\n", "    if question in ['q', 'exit', 'quit']:\n", "        break\n", "    answer = app.query(question)\n", "    print(answer)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}