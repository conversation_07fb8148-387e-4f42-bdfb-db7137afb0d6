{"cells": [{"cell_type": "markdown", "metadata": {"id": "b02n_zJ_hl3d"}, "source": ["## Cookbook for using OpenAI with Embedchain"]}, {"cell_type": "markdown", "metadata": {"id": "gyJ6ui2vhtMY"}, "source": ["### Step-1: Install embedchain package"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "-NbXjAdlh0vJ", "outputId": "6c630676-c7fc-4054-dc94-c613de58a037"}, "outputs": [], "source": ["!pip install embedchain"]}, {"cell_type": "markdown", "metadata": {"id": "nGnpSYAAh2bQ"}, "source": ["### Step-2: Set OpenAI environment variables\n", "\n", "You can find this env variable on your [OpenAI dashboard](https://platform.openai.com/account/api-keys)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "0fBdQ9GAiRvK"}, "outputs": [], "source": ["import os\n", "from embedchain import App\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-xxx\""]}, {"cell_type": "markdown", "metadata": {"id": "PGt6uPLIi1CS"}, "source": ["### Step-3 Create embedchain app and define your config"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Amzxk3m-i3tD"}, "outputs": [], "source": ["app = App.from_config(config={\n", "    \"llm\": {\n", "        \"provider\": \"openai\",\n", "        \"config\": {\n", "            \"model\": \"gpt-4o-mini\",\n", "            \"temperature\": 0.5,\n", "            \"max_tokens\": 1000,\n", "            \"top_p\": 1,\n", "            \"stream\": False\n", "        }\n", "    },\n", "    \"embedder\": {\n", "        \"provider\": \"openai\",\n", "        \"config\": {\n", "            \"model\": \"text-embedding-ada-002\"\n", "        }\n", "    }\n", "})"]}, {"cell_type": "markdown", "metadata": {"id": "XNXv4yZwi7ef"}, "source": ["### Step-4: Add data sources to your app"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Sn_0rx9QjIY9"}, "outputs": [], "source": ["app.add(\"https://www.forbes.com/profile/elon-musk\")"]}, {"cell_type": "markdown", "metadata": {"id": "_7W6fDeAjMAP"}, "source": ["### Step-5: All set. Now start asking questions related to your data"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "cvIK7dWRjN_f"}, "outputs": [], "source": ["while(True):\n", "    question = input(\"Enter question: \")\n", "    if question in ['q', 'exit', 'quit']:\n", "        break\n", "    answer = app.query(question)\n", "    print(answer)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 0}