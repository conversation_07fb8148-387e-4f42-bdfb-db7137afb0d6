{"cells": [{"cell_type": "markdown", "metadata": {"id": "b02n_zJ_hl3d"}, "source": ["## Cookbook for using Cohere with Embedchain"]}, {"cell_type": "markdown", "metadata": {"id": "gyJ6ui2vhtMY"}, "source": ["### Step-1: Install embedchain package"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "-NbXjAdlh0vJ", "outputId": "fae77912-4e6a-4c78-fcb7-fbbe46f7a9c7"}, "outputs": [], "source": ["!pip install embedchain[cohere]"]}, {"cell_type": "markdown", "metadata": {"id": "nGnpSYAAh2bQ"}, "source": ["### Step-2: Set Cohere related environment variables\n", "\n", "You can find `OPENAI_API_KEY` on your [OpenAI dashboard](https://platform.openai.com/account/api-keys) and `COHERE_API_KEY` key on your [Cohere dashboard](https://dashboard.cohere.com/api-keys)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "0fBdQ9GAiRvK"}, "outputs": [], "source": ["import os\n", "from embedchain import App\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-xxx\"\n", "os.environ[\"COHERE_API_KEY\"] = \"xxx\""]}, {"cell_type": "markdown", "metadata": {"id": "PGt6uPLIi1CS"}, "source": ["### Step-3 Create embedchain app and define your config"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 321}, "id": "Amzxk3m-i3tD", "outputId": "afe8afde-5cb8-46bc-c541-3ad26cc3fa6e"}, "outputs": [], "source": ["app = App.from_config(config={\n", "    \"provider\": \"cohere\",\n", "    \"config\": {\n", "        \"model\": \"gptd-instruct-tft\",\n", "        \"temperature\": 0.5,\n", "        \"max_tokens\": 1000,\n", "        \"top_p\": 1,\n", "        \"stream\": False\n", "    }\n", "})"]}, {"cell_type": "markdown", "metadata": {"id": "XNXv4yZwi7ef"}, "source": ["### Step-4: Add data sources to your app"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 176}, "id": "Sn_0rx9QjIY9", "outputId": "2f2718a4-3b7e-4844-fd46-3e0857653ca0"}, "outputs": [], "source": ["app.add(\"https://www.forbes.com/profile/elon-musk\")"]}, {"cell_type": "markdown", "metadata": {"id": "_7W6fDeAjMAP"}, "source": ["### Step-5: All set. Now start asking questions related to your data"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cvIK7dWRjN_f", "outputId": "79e873c8-9594-45da-f5a3-0a893511267f"}, "outputs": [], "source": ["while(True):\n", "    question = input(\"Enter question: \")\n", "    if question in ['q', 'exit', 'quit']:\n", "        break\n", "    answer = app.query(question)\n", "    print(answer)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}