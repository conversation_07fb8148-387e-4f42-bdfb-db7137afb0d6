{"cells": [{"cell_type": "markdown", "metadata": {"id": "b02n_zJ_hl3d"}, "source": ["## Cookbook for using LLAMA2 with Embedchain"]}, {"cell_type": "markdown", "metadata": {"id": "gyJ6ui2vhtMY"}, "source": ["### Step-1: Install embedchain package"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "-NbXjAdlh0vJ", "outputId": "86a4a9b2-4ed6-431c-da6f-c3eacb390f42"}, "outputs": [], "source": ["!pip install embedchain[llama2]"]}, {"cell_type": "markdown", "metadata": {"id": "nGnpSYAAh2bQ"}, "source": ["### Step-2: Set LLAMA2 related environment variables\n", "\n", "You can find `OPENAI_API_KEY` on your [OpenAI dashboard](https://platform.openai.com/account/api-keys) and `REPLICATE_API_TOKEN` key on your [Replicate dashboard](https://replicate.com/account/api-tokens)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "0fBdQ9GAiRvK"}, "outputs": [], "source": ["import os\n", "from embedchain import App\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-xxx\"\n", "os.environ[\"REPLICATE_API_TOKEN\"] = \"xxx\""]}, {"cell_type": "markdown", "metadata": {"id": "PGt6uPLIi1CS"}, "source": ["### Step-3 Create embedchain app and define your config"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Amzxk3m-i3tD"}, "outputs": [], "source": ["app = App.from_config(config={\n", "    \"provider\": \"llama2\",\n", "    \"config\": {\n", "        \"model\": \"a16z-infra/llama13b-v2-chat:df7690f1994d94e96ad9d568eac121aecf50684a0b0963b25a41cc40061269e5\",\n", "        \"temperature\": 0.5,\n", "        \"max_tokens\": 1000,\n", "        \"top_p\": 0.5,\n", "        \"stream\": False\n", "    }\n", "})"]}, {"cell_type": "markdown", "metadata": {"id": "XNXv4yZwi7ef"}, "source": ["### Step-4: Add data sources to your app"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 52}, "id": "Sn_0rx9QjIY9", "outputId": "ba158e9c-0f16-4c6b-a876-7543120985a2"}, "outputs": [], "source": ["app.add(\"https://www.forbes.com/profile/elon-musk\")"]}, {"cell_type": "markdown", "metadata": {"id": "_7W6fDeAjMAP"}, "source": ["### Step-5: All set. Now start asking questions related to your data"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 599}, "id": "cvIK7dWRjN_f", "outputId": "e2d11a25-a2ed-4034-ec6a-e8a5986c89ae"}, "outputs": [], "source": ["while(True):\n", "    question = input(\"Enter question: \")\n", "    if question in ['q', 'exit', 'quit']:\n", "        break\n", "    answer = app.query(question)\n", "    print(answer)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}