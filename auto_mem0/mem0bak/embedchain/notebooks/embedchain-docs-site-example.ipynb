{"cells": [{"cell_type": "code", "execution_count": 1, "id": "e9a9dc6a", "metadata": {}, "outputs": [], "source": ["from embedchain import App\n", "\n", "embedchain_docs_bot = App()"]}, {"cell_type": "code", "execution_count": 2, "id": "c1c24d68", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["All data from https://docs.embedchain.ai/ already exists in the database.\n"]}], "source": ["embedchain_docs_bot.add(\"docs_site\", \"https://docs.embedchain.ai/\")"]}, {"cell_type": "code", "execution_count": 3, "id": "48cdaecf", "metadata": {}, "outputs": [], "source": ["answer = embedchain_docs_bot.query(\"Write a flask API for embedchain bot\")"]}, {"cell_type": "code", "execution_count": 4, "id": "0fe18085", "metadata": {}, "outputs": [{"data": {"text/markdown": ["To write a Flask API for the embedchain bot, you can use the following code snippet:\n", "\n", "```python\n", "from flask import Flask, request, jsonify\n", "from embedchain import App\n", "\n", "app = Flask(__name__)\n", "bot = App()\n", "\n", "# Add datasets to the bot\n", "bot.add(\"youtube_video\", \"https://www.youtube.com/watch?v=3qHkcs3kG44\")\n", "bot.add(\"pdf_file\", \"https://navalmanack.s3.amazonaws.com/<PERSON>_The-Almanack-of-Naval-Ravikant_Final.pdf\")\n", "\n", "@app.route('/query', methods=['POST'])\n", "def query():\n", "    data = request.get_json()\n", "    question = data['question']\n", "    response = bot.query(question)\n", "    return jsonify({'response': response})\n", "\n", "if __name__ == '__main__':\n", "    app.run()\n", "```\n", "\n", "In this code, we create a Flask app and initialize an instance of the embedchain bot. We then add the desired datasets to the bot using the `add()` function.\n", "\n", "Next, we define a route `/query` that accepts POST requests. The request body should contain a JSON object with a `question` field. The bot's `query()` function is called with the provided question, and the response is returned as a JSON object.\n", "\n", "Finally, we run the Flask app using `app.run()`.\n", "\n", "Note: Make sure to install Flask and embedchain packages before running this code."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Markdown\n", "# Create a Markdown object and display it\n", "markdown_answer = Markdown(answer)\n", "display(markdown_answer)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}