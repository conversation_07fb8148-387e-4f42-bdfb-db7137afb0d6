---
title: "📱 Telegram Bot"
---

### 🖼️ Template Setup

- Open the Telegram app and search for the `<PERSON><PERSON><PERSON><PERSON>` user.
- Start a chat with <PERSON><PERSON><PERSON><PERSON> and use the `/newbot` command to create a new bot.
- Follow the instructions to choose a name and username for your bot.
- Once the bot is created, Bo<PERSON><PERSON>ather will provide you with a unique token for your bot.

<Tabs>
    <Tab title="docker">
        ```bash
        docker run --name telegram-bot -e OPENAI_API_KEY=sk-xxx -e TELEGRAM_BOT_TOKEN=xxx -p 8000:8000 embedchain/telegram-bot
        ```

    <Note>
    If you wish to use **Docker**, you would need to host your bot on a server.
    You can use [ngrok](https://ngrok.com/) to expose your localhost to the
    internet and then set the webhook using the ngrok URL.
    </Note>

    </Tab>
    <Tab title="replit">
    <Card>
        Fork <ins>**[this](https://replit.com/@taranjeetio/EC-Telegram-Bot-Template?v=1#README.md)**</ins> replit template.
    </Card>

    - Set your `OPENAI_API_KEY` in Secrets.
    - Set the unique token as `TELEGRAM_BOT_TOKEN` in Secrets.

    </Tab>

</Tabs>

- Click on `Run` in the replit container and a URL will get generated for your bot.
- Now set your webhook by running the following link in your browser:

```url
https://api.telegram.org/bot<Your_Telegram_Bot_Token>/setWebhook?url=<Replit_Generated_URL>
```

- When you get a successful response in your browser, your bot is ready to be used.

### 🚀 Usage Instructions

- Open your bot by searching for it using the bot name or bot username.
- Click on `Start` or type `/start` and follow the on screen instructions.

🎉 Happy Chatting! 🎉
