# fly.toml app configuration file generated for ec-app-crimson-dew-123 on 2024-01-04T06:48:40+05:30
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = "ec-app-crimson-dew-123"
primary_region = "sjc"

[build]

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 0
  processes = ["app"]

[[vm]]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 1024
