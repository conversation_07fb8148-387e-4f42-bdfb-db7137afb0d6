[Cloudflare Vectorize](https://developers.cloudflare.com/vectorize/) is a vector database offering from Cloudflare, allowing you to build AI-powered applications with vector embeddings.

### Usage

<CodeGroup>
```typescript TypeScript
import { Memory } from 'mem0ai/oss';

const config = {
  vectorStore: {
    provider: 'vectorize',
    config: {
      indexName: 'my-memory-index',
      accountId: 'your-cloudflare-account-id',
      apiKey: 'your-cloudflare-api-key',
      dimension: 1536, // Optional: defaults to 1536
    },
  },
};

const memory = new Memory(config);
const messages = [
    {"role": "user", "content": "I'm looking for a good book to read."},
    {"role": "assistant", "content": "Sure, what genre are you interested in?"},
    {"role": "user", "content": "I enjoy fantasy novels with strong world-building."},
    {"role": "assistant", "content": "Great! I'll keep that in mind for future recommendations."}
]
await memory.add(messages, { userId: "bob", metadata: { interest: "books" } });
```
</CodeGroup>

### Config

Let's see the available parameters for the `vectorize` config:

<Tabs>
<Tab title="TypeScript">
| Parameter | Description | Default Value |
| --- | --- | --- |
| `indexName` | The name of the Vectorize index | `None` (Required) |
| `accountId` | Your Cloudflare account ID | `None` (Required) |
| `apiKey` | Your Cloudflare API token | `None` (Required) |
| `dimension` | Dimensions of the embedding model | `1536` |
</Tab>
</Tabs>
