---
title: Together
---

To use Together embedding models, set the `TOGETHER_API_KEY` environment variable. You can obtain the Together API key from the [Together Platform](https://api.together.xyz/settings/api-keys).

### Usage

<Note> The `embedding_model_dims` parameter for `vector_store` should be set to `768` for Together embedder. </Note>

```python
import os
from mem0 import Memory

os.environ["TOGETHER_API_KEY"] = "your_api_key"
os.environ["OPENAI_API_KEY"] = "your_api_key" # For LLM

config = {
    "embedder": {
        "provider": "together",
        "config": {
            "model": "togethercomputer/m2-bert-80M-8k-retrieval"
        }
    }
}

m = Memory.from_config(config)
messages = [
    {"role": "user", "content": "I'm planning to watch a movie tonight. Any recommendations?"},
    {"role": "assistant", "content": "How about a thriller movies? They can be quite engaging."},
    {"role": "user", "content": "I’m not a big fan of thriller movies but I love sci-fi movies."},
    {"role": "assistant", "content": "Got it! I'll avoid thriller recommendations and suggest sci-fi movies in the future."}
]
m.add(messages, user_id="john")
```

### Config

Here are the parameters available for configuring Together embedder:

| Parameter | Description | Default Value |
| --- | --- | --- |
| `model` | The name of the embedding model to use | `togethercomputer/m2-bert-80M-8k-retrieval` |
| `embedding_dims` | Dimensions of the embedding model | `768` |
| `api_key` | The Together API key | `None` |
