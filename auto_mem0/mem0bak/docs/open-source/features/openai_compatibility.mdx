---
title: OpenAI Compatibility
icon: "code"
iconType: "solid"
---

<Snippet file="security-compliance.mdx" />

Mem0 can be easily integrated into chat applications to enhance conversational agents with structured memory. Mem0's APIs are designed to be compatible with OpenAI's, with the goal of making it easy to leverage Mem0 in applications you may have already built.

If you have a `Mem0 API key`, you can use it to initialize the client. Alternatively, you can initialize Mem0 without an API key if you're using it locally.

Mem0 supports several language models (LLMs) through integration with various [providers](https://litellm.vercel.app/docs/providers).

## Use Mem0 Platform

```python
from mem0.proxy.main import Mem0

client = Mem0(api_key="m0-xxx")

# First interaction: Storing user preferences
messages = [
    {
        "role": "user",
        "content": "I love indian food but I cannot eat pizza since allergic to cheese."
    },
]
user_id = "alice"
chat_completion = client.chat.completions.create(
    messages=messages,
    model="gpt-4o-mini",
    user_id=user_id
)
# Memory saved after this will look like: "Loves Indian food. Allergic to cheese and cannot eat pizza."

# Second interaction: Leveraging stored memory
messages = [
    {
        "role": "user",
        "content": "Suggest restaurants in San Francisco to eat.",
    }
]

chat_completion = client.chat.completions.create(
    messages=messages,
    model="gpt-4o-mini",
    user_id=user_id
)
print(chat_completion.choices[0].message.content)
# Answer: You might enjoy Indian restaurants in San Francisco, such as Amber India, Dosa, or Curry Up Now, which offer delicious options without cheese.
```

In this example, you can see how the second response is tailored based on the information provided in the first interaction. Mem0 remembers the user's preference for Indian food and their cheese allergy, using this information to provide more relevant and personalized restaurant suggestions in San Francisco.

### Use Mem0 OSS

```python
config = {
    "vector_store": {
        "provider": "qdrant",
        "config": {
            "host": "localhost",
            "port": 6333,
        }
    },
}

client = Mem0(config=config)

chat_completion = client.chat.completions.create(
    messages=[
        {
            "role": "user",
            "content": "What's the capital of France?",
        }
    ],
    model="gpt-4o",
)
```

## Mem0 Params for Chat Completion

- `user_id` (Optional[str]): Identifier for the user.

- `agent_id` (Optional[str]): Identifier for the agent.

- `run_id` (Optional[str]): Identifier for the run.

- `metadata` (Optional[dict]): Additional metadata to be stored with the memory.

- `filters` (Optional[dict]): Filters to apply when searching for relevant memories.

- `limit` (Optional[int]): Maximum number of relevant memories to retrieve. Default is 10.


Other parameters are similar to OpenAI's API, making it easy to integrate Mem0 into your existing applications.
