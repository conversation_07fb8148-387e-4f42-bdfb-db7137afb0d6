"""
增强的Memory类
将memory.add()拆分成两个独立接口：extract_memories()和store_memories()
这样可以在外部添加记忆类别，然后再调用存储接口
"""

import sys
import os
import json
import uuid
import hashlib
import datetime
import pytz
from typing import Dict, List, Any, Optional, Tuple
from copy import deepcopy
from openai import OpenAI

# 添加本地mem0源码路径到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
mem0_path = os.path.join(current_dir, 'mem4lm')
sys.path.insert(0, mem0_path)

import logging
from mem0 import Memory
from mem0.memory.utils import parse_messages, get_fact_retrieval_messages, remove_code_blocks

logger = logging.getLogger('enhanced_memory.py')
# 确保logger能够输出
logger.setLevel(logging.DEBUG)

api_key = "1s3963nw8802M4O55yMuU6x37tOYQ682"  # 替换为你的 API 密钥.
api_base = "http://58.22.103.26:8099/v1"  # 替换为你的服务地址
model_name="SenseAuto-Chat"  # 替换为你的模型名称
classify_llm = OpenAI(api_key=api_key, base_url=api_base)

class ExtractedMemory:
    """提取出的记忆对象"""

    def __init__(self, text: str, memory_id: str = None, event: str = "ADD",
                 old_memory: str = None, embeddings: List[float] = None):
        self.text = text
        self.memory_id = memory_id or str(uuid.uuid4())
        self.event = event  # ADD, UPDATE, DELETE, NONE, GET
        self.old_memory = old_memory
        self.embeddings = embeddings
        self.category = None  # 外部设置的类别
        self.metadata = {}    # 外部设置的元数据
        self.score = None     # 相关性分数（用于GET事件）
    
    def set_category(self, category: str):
        """设置记忆类别"""
        self.category = category
        self.metadata["category"] = category
        return self
    
    def set_metadata(self, metadata: Dict[str, Any]):
        """设置记忆元数据"""
        self.metadata.update(metadata)
        return self
    
    def to_dict(self):
        """转换为字典格式"""
        result = {
            "id": self.memory_id,
            "memory": self.text,  # 使用memory字段名，与mem0保持一致
            "event": self.event,
            "category": self.category,
            "metadata": self.metadata
        }

        if self.old_memory:
            result["old_memory"] = self.old_memory
        if self.score is not None:
            result["score"] = self.score

        return result

class EnhancedMemory(Memory):
    """增强的Memory类，支持分离的提取和存储操作"""
    
    def __init__(self, config=None):
        """初始化增强的Memory类"""
        super().__init__(config)
    
    def add_with_classification(self, messages, classifier_func, *, user_id: Optional[str] = None,
                               agent_id: Optional[str] = None, run_id: Optional[str] = None,
                               base_metadata: Optional[Dict[str, Any]] = None) -> Dict:
        """
        使用Mem0原生add方法并添加分类功能

        Args:
            messages: 对话消息
            classifier_func: 分类函数
            user_id: 用户ID
            agent_id: 代理ID
            run_id: 运行ID
            base_metadata: 基础元数据

        Returns:
            Dict: 添加结果，包含事件类型和分类信息
        """
        logger.info(f"🧠 开始处理记忆（使用Mem0原生事件检测）...")

        try:
            # 直接使用Mem0原生的add方法
            result = super().add(
                messages,
                user_id=user_id,
                agent_id=agent_id,
                run_id=run_id,
                metadata=base_metadata or {},
            )

            # 解析结果
            memories_with_events = result.get('results', []) if isinstance(result, dict) else result

            if not memories_with_events:
                logger.info("✅ 没有提取到记忆")
                return {"results": []}

            logger.info(f"✅ 成功处理 {len(memories_with_events)} 条记忆")

            # 为每个记忆添加分类信息
            classified_results = []
            for i, mem_result in enumerate(memories_with_events, 1):
                memory_text = mem_result.get('memory', '')
                event_type = mem_result.get('event', 'ADD')
                memory_id = mem_result.get('id')
                old_memory = mem_result.get('previous_memory')

                # UPDATE事件和NONE事件跳过分类
                if event_type == "UPDATE":
                    # 获取原有分类信息
                    original_category = "general"
                    original_metadata = {}

                    if memory_id:
                        try:
                            existing_memory = self.vector_store.get(vector_id=memory_id)
                            if existing_memory and existing_memory.payload:
                                original_category = existing_memory.payload.get("category", "general")
                                original_metadata = existing_memory.payload.copy()
                        except Exception as e:
                            logger.warning(f"  ⚠️ 获取原有分类失败: {e}")

                    logger.info(f"  {i}. 🔄 [{event_type}] [{original_category}] {memory_text} (保持原有分类)")
                    if old_memory:
                        logger.debug(f"      旧记忆: {old_memory}")

                    # 添加原有分类信息到结果
                    classified_result = mem_result.copy()
                    classified_result["category"] = original_category
                    classified_result["metadata"] = original_metadata
                    classified_results.append(classified_result)
                elif event_type == "NONE":
                    # NONE事件：什么都不做，直接跳过
                    logger.info(f"  {i}. ⏭️ [{event_type}] {memory_text} (无操作，跳过)")
                    if old_memory:
                        logger.debug(f"      旧记忆: {old_memory}")
                    # 不添加到classified_results，直接跳过
                else:
                    # 其他事件进行分类
                    try:
                        category, metadata = classifier_func(memory_text)

                        # 更新记忆的分类信息
                        if memory_id:
                            try:
                                existing_memory = self.vector_store.get(vector_id=memory_id)
                                if existing_memory and existing_memory.payload:
                                    updated_metadata = existing_memory.payload.copy()
                                    updated_metadata.update(metadata)
                                    updated_metadata["category"] = category

                                    self.vector_store.update(
                                        vector_id=memory_id,
                                        payload=updated_metadata
                                    )
                            except Exception as e:
                                logger.warning(f"  ⚠️ 更新分类失败: {e}")

                        logger.info(f"  {i}. ✅ [{event_type}] [{category}] {memory_text}")
                        if old_memory:
                            logger.debug(f"      旧记忆: {old_memory}")

                        # 添加分类信息到结果
                        classified_result = mem_result.copy()
                        classified_result["category"] = category
                        classified_result["metadata"] = metadata
                        classified_results.append(classified_result)

                    except Exception as e:
                        logger.warning(f"  {i}. ⚠️ 分类失败: {e}")
                        classified_result = mem_result.copy()
                        classified_result["category"] = "general"
                        classified_results.append(classified_result)

            return {"results": classified_results}

        except Exception as e:
            logger.error(f"Error in add_with_classification: {e}")
            logger.error(f"❌ 记忆处理失败: {e}")
            return {"results": []}

    def extract_memories(self, messages, *, user_id: Optional[str] = None,
                        agent_id: Optional[str] = None, run_id: Optional[str] = None) -> List[ExtractedMemory]:
        """
        向后兼容的extract_memories方法

        注意：这个方法主要用于向后兼容，推荐使用 add_with_classification 方法
        """

        # 使用原生add方法获取结果
        try:
            result = super().add(
                messages,
                user_id=user_id,
                agent_id=agent_id,
                run_id=run_id,
                metadata={},
            )

            memories_with_events = result.get('results', []) if isinstance(result, dict) else result

            # 转换为ExtractedMemory对象
            extracted_memories = []
            for mem_result in memories_with_events:
                memory_text = mem_result.get('memory', '')
                event_type = mem_result.get('event', 'ADD')
                memory_id = mem_result.get('id')
                old_memory = mem_result.get('previous_memory')

                # 生成嵌入向量
                try:
                    embeddings = self.embedding_model.embed(memory_text, "add")
                    if not embeddings or len(embeddings) == 0:
                        logger.warning(f"⚠️ 嵌入向量为空，跳过记忆: {memory_text[:50]}...")
                        continue
                except Exception as embed_error:
                    logger.error(f"❌ 生成嵌入向量失败: {embed_error}")
                    continue

                extracted_memory = ExtractedMemory(
                    text=memory_text,
                    memory_id=memory_id,
                    event=event_type,
                    old_memory=old_memory,
                    embeddings=embeddings
                )
                extracted_memories.append(extracted_memory)

            return extracted_memories

        except Exception as e:
            logger.error(f"Error in extract_memories: {e}")
            return []

    def store_memories(self, extracted_memories: List[ExtractedMemory],
                      *, user_id: Optional[str] = None, agent_id: Optional[str] = None,
                      run_id: Optional[str] = None, base_metadata: Optional[Dict[str, Any]] = None) -> Dict:
        """
        更新已存储记忆的分类信息

        注意：记忆已经通过extract_memories中的原生add方法存储了，
        这里只需要更新分类和元数据信息

        Args:
            extracted_memories: 提取并分类的记忆列表
            user_id: 用户ID
            agent_id: 代理ID
            run_id: 运行ID
            base_metadata: 基础元数据

        Returns:
            Dict: 存储结果
        """
        logger.info(f"💾 开始更新 {len(extracted_memories)} 条记忆的分类信息...")

        stored_results = []

        for i, memory in enumerate(extracted_memories, 1):
            try:
                # 验证memory对象的基本属性
                if not hasattr(memory, 'memory_id') or not hasattr(memory, 'text') or not hasattr(memory, 'event'):
                    logger.warning(f"  {i}. ⚠️ 记忆对象缺少必要属性，跳过")
                    continue

                # 处理不同类型的事件
                if memory.memory_id:
                    try:
                        existing_memory = self.vector_store.get(vector_id=memory.memory_id)
                        if existing_memory:
                            if memory.event == "UPDATE":
                                # UPDATE事件：只更新记忆内容和向量，保持原有分类
                                logger.info(f"  {i}. 🔄 更新记忆内容: {memory.memory_id}")

                                # 生成新的嵌入向量
                                if memory.embeddings:
                                    new_embeddings = memory.embeddings
                                else:
                                    try:
                                        new_embeddings = self.embedding_model.embed(memory.text, "update")
                                        if not new_embeddings or len(new_embeddings) == 0:
                                            logger.warning(f"  {i}. ⚠️ 嵌入向量为空，跳过更新")
                                            continue
                                    except Exception as embed_error:
                                        logger.error(f"  {i}. ❌ 生成嵌入向量失败: {embed_error}")
                                        continue

                                # 更新元数据，保持原有分类
                                updated_metadata = existing_memory.payload.copy() if existing_memory.payload else {}
                                # 只更新非分类相关的元数据
                                for key, value in memory.metadata.items():
                                    if key != "category":  # 保持原有分类不变
                                        updated_metadata[key] = value
                                updated_metadata["memory"] = memory.text  # 更新记忆文本

                                # 获取原有分类信息
                                original_category = existing_memory.payload.get("category", "general") if existing_memory.payload else "general"

                                # 更新向量存储（包括内容和嵌入）
                                try:
                                    self.vector_store.update(
                                        vector_id=memory.memory_id,
                                        vector=new_embeddings,
                                        payload=updated_metadata
                                    )
                                except Exception as update_error:
                                    logger.error(f"  {i}. ❌ 向量存储更新失败: {update_error}")
                                    continue
                                logger.info(f"  {i}. ✅ [{memory.event}] [{original_category}] {memory.text}")
                                if memory.old_memory:
                                    logger.debug(f"      旧记忆: {memory.old_memory}")

                                # 更新结果中的分类信息为原有分类
                                memory.category = original_category
                            elif memory.event == "NONE":
                                # NONE事件：什么都不做
                                logger.info(f"  {i}. ⏭️ [{memory.event}] {memory.text} (无操作，跳过)")
                            else:
                                # 其他事件：只更新分类信息
                                updated_metadata = existing_memory.payload.copy() if existing_memory.payload else {}
                                updated_metadata.update(memory.metadata)
                                updated_metadata["category"] = memory.category

                                try:
                                    self.vector_store.update(
                                        vector_id=memory.memory_id,
                                        payload=updated_metadata
                                    )
                                except Exception as update_error:
                                    logger.error(f"  {i}. ❌ 元数据更新失败: {update_error}")
                                    continue
                                logger.info(f"  {i}. ✅ [{memory.event}] [{memory.category}] {memory.text}")
                        else:
                            logger.warning(f"  {i}. ⚠️ 无法找到记忆 {memory.memory_id}")
                    except Exception as e:
                        logger.error(f"  {i}. ❌ 更新失败: {e}")
                else:
                    logger.warning(f"  {i}. ⚠️ 缺少memory_id，无法更新")

                result = {
                    "id": memory.memory_id,
                    "memory": memory.text,
                    "event": memory.event,
                    "category": memory.category
                }

                if memory.old_memory:
                    result["old_memory"] = memory.old_memory

                stored_results.append(result)

            except Exception as e:
                logger.error(f"Error updating memory classification {memory.text}: {e}")
                logger.error(f"  {i}. ❌ 分类更新失败: [{memory.event}] {memory.text}")

        logger.info(f"✅ 成功更新 {len(stored_results)} 条记忆分类")

        return {"results": stored_results}
    
    def _create_memory_direct(self, data: str, embeddings: List[float], metadata: Dict[str, Any]) -> str:
        """直接创建记忆，不重新计算嵌入向量"""
        memory_id = str(uuid.uuid4())
        
        # 准备元数据
        final_metadata = metadata.copy()
        final_metadata["data"] = data
        final_metadata["hash"] = hashlib.md5(data.encode()).hexdigest()
        final_metadata["created_at"] = datetime.datetime.now(pytz.timezone("US/Pacific")).isoformat()
        
        # 存储到向量数据库
        self.vector_store.insert(
            vectors=[embeddings],
            ids=[memory_id],
            payloads=[final_metadata],
        )
        
        # 添加历史记录
        self.db.add_history(
            memory_id,
            None,
            data,
            "ADD",
            created_at=final_metadata.get("created_at"),
            actor_id=final_metadata.get("actor_id"),
            role=final_metadata.get("role"),
        )
        
        return memory_id

    def _update_memory_direct(self, memory_id: str, data: str, embeddings: List[float], metadata: Dict[str, Any], old_memory: str = None) -> str:
        """直接更新记忆，替换现有记忆内容"""

        # 准备元数据
        final_metadata = metadata.copy()
        final_metadata["data"] = data
        final_metadata["hash"] = hashlib.md5(data.encode()).hexdigest()
        final_metadata["updated_at"] = datetime.datetime.now(pytz.timezone("US/Pacific")).isoformat()

        # 保留原始创建时间
        try:
            existing_memory = self.vector_store.get(vector_id=memory_id)
            if existing_memory and existing_memory.payload:
                final_metadata["created_at"] = existing_memory.payload.get("created_at")
        except Exception as e:
            logger.warning(f"获取现有记忆失败: {e}")
            final_metadata["created_at"] = final_metadata["updated_at"]

        # 删除旧记忆
        try:
            self.vector_store.delete(vector_id=memory_id)
            logger.debug(f"    🗑️ 删除旧记忆: {memory_id}")
        except Exception as e:
            logger.warning(f"    ⚠️ 删除旧记忆失败: {e}")

        # 插入更新后的记忆（使用相同的ID）
        self.vector_store.insert(
            vectors=[embeddings],
            ids=[memory_id],
            payloads=[final_metadata],
        )
        logger.debug(f"    ✅ 插入更新记忆: {memory_id}")

        # 添加历史记录
        self.db.add_history(
            memory_id,
            old_memory,
            data,
            "UPDATE",
            created_at=final_metadata.get("updated_at"),
            actor_id=final_metadata.get("actor_id"),
            role=final_metadata.get("role"),
        )

        return memory_id

    def extract_and_classify_memories(self, messages, classifier_func,
                                    *, user_id: Optional[str] = None, 
                                    agent_id: Optional[str] = None, 
                                    run_id: Optional[str] = None,
                                    base_metadata: Optional[Dict[str, Any]] = None) -> Dict:
        """
        一站式提取、分类和存储记忆
        
        Args:
            messages: 对话消息
            classifier_func: 分类函数，接收记忆文本，返回类别和元数据
            user_id: 用户ID
            agent_id: 代理ID
            run_id: 运行ID
            base_metadata: 基础元数据
            
        Returns:
            Dict: 处理结果
        """
        logger.info(f"\n🔄 一站式记忆处理流程")

        # 直接使用新的add_with_classification方法
        result = self.add_with_classification(
            messages,
            classifier_func,
            user_id=user_id,
            agent_id=agent_id,
            run_id=run_id,
            base_metadata=base_metadata
        )

        # 统计结果
        stored_count = len(result.get("results", []))

        return {
            "results": result.get("results", []),
            "stored_count": stored_count,
            "message": f"Successfully processed {stored_count} memories"
        }

def create_classifier_function(categories_config: Dict[str, Dict[str, str]],
                             default_category: str = "general",
                             llm_config: Optional[Dict] = None):
    """
    创建基于LLM的智能分类函数

    Args:
        categories_config: 类别配置字典，格式为:
            {
                "category_name": {
                    "description": "类别描述",
                    "examples": "示例说明"
                }
            }
        default_category: 默认类别
        llm_config: LLM配置信息

    Returns:
        分类函数
    """
    # 初始化LLM
    llm = classify_llm

    def classify_memory(memory_text: str) -> tuple:
        """
        使用LLM进行记忆文本分类

        Args:
            memory_text: 记忆文本

        Returns:
            tuple: (category, metadata)
        """
        return _classify_with_llm(memory_text, categories_config, llm, default_category)

    return classify_memory
def filter_think_tags(text):
    """移除文本中的 <think>...</think> 标签及其内容"""
    import re
    return re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL | re.IGNORECASE).strip()
def _classify_with_llm(memory_text: str, categories_config: Dict[str, Dict[str, str]],
                      llm, default_category: str) -> tuple:
    """
    使用LLM进行分类
    """
    # 构建分类规则描述
    category_descriptions = []
    allowed_categories = list(categories_config.keys())

    for category, config in categories_config.items():
        description = config.get("description", "")
        examples = config.get("examples", "")

        category_info = f"- **{category}**: {description}"
        if examples:
            category_info += f"\n  示例: {examples}"

        category_descriptions.append(category_info)

    # 构建分类prompt
    classification_prompt = f"""你是一个专业的记忆分类助手。请将给定的记忆文本分类到最合适的类别中。

记忆文本: "{memory_text}"

可选类别及其详细说明:
{chr(10).join(category_descriptions)}

分类规则:
1. 仔细分析记忆文本的主要内容和语义
2. 根据类别描述和示例，选择最符合文本内容的类别
3. 如果文本包含多种类型信息，选择最主要、最核心的类别
4. 如果无法确定具体类别，选择 "{default_category}"
5. 分类时要考虑文本的深层含义，不仅仅是表面关键词

请返回JSON格式的结果，包含以下字段:
- category: 选择的类别名称（必须是上述类别之一）
- confidence: 分类置信度 (0.0-1.0，1.0表示非常确定)
- reasoning: 分类理由 (详细说明为什么选择这个类别)

示例输出:
{{"category": "personal_info", "confidence": 0.9, "reasoning": "文本包含个人姓名和职业信息，属于个人基本信息类别"}}

请确保category字段的值必须是上述可选类别之一。"""

    try:
        # 调用LLM进行分类
        # 使用OpenAI客户端的正确方法
        completion = llm.chat.completions.create(
            model=model_name,
            messages=[{"role": "user", "content": classification_prompt}],
            response_format={"type": "json_object"},
            temperature=0.1
        )
        response = completion.choices[0].message.content

        # 解析LLM响应
        #去掉think标签
        response = filter_think_tags(response)
        logger.debug(f"llm classification response: {response}")
        result = json.loads(response)

        category = result.get("category", default_category)
        confidence = float(result.get("confidence", 0.5))
        reasoning = result.get("reasoning", "LLM分类")

        # 验证类别是否在允许列表中
        if category not in allowed_categories:
            logger.warning(f"LLM returned invalid category '{category}', using default")
            category = default_category
            confidence = 0.3
            reasoning = f"LLM返回无效类别'{category}'，使用默认类别"

        # 构建元数据
        metadata = {
            "classification_confidence": confidence,
            "classification_method": "llm_classification",
            "classification_reasoning": reasoning,
            "llm_response": response[:200] + "..." if len(response) > 200 else response,
            "available_categories": ",".join(allowed_categories)
        }

        return category, metadata

    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse LLM response as JSON: {e}")
        logger.error(f"LLM response was: {response}")
        raise Exception(f"LLM response parsing failed: {e}")
    except Exception as e:
        logger.error(f"LLM classification error: {e}")
        raise e



# 使用示例
if __name__ == "__main__":
    # 配置
    config = {
        "vector_store": {
            "provider": "chroma",
            "config": {
                "collection_name": "enhanced_memory_test",
                "path": "./db/enhanced_memory_db"
            }
        },
        "llm": {
            "provider": "ollama",
            "config": {
                "model": "qwen3:8b",
                "temperature": 0.1,
                "ollama_base_url": "http://************:8088"
            }
        },
        "embedder": {
            "provider": "ollama",
            "config": {
                "model": "qwen3:8b",
                "ollama_base_url": "http://************:8088"
            }
        }
    }
    
    # 创建增强的Memory实例
    enhanced_memory = EnhancedMemory.from_config(config)
    
    # 定义分类配置
    categories_config = {
        "personal_info": {
            "description": "个人基本信息，包括姓名、年龄、职业、住址等",
            "examples": "我叫张三、今年25岁、是一名工程师"
        },
        "preferences": {
            "description": "个人偏好和喜好，包括饮食偏好、娱乐爱好等",
            "examples": "喜欢喝咖啡、不喜欢辣食、爱听音乐"
        },
        "work_tasks": {
            "description": "工作相关的任务、项目、会议、截止日期等",
            "examples": "项目deadline、明天开会、完成开发"
        },
        "technical_knowledge": {
            "description": "技术知识和技能，包括编程语言、框架、工具等",
            "examples": "学习Python、掌握React、了解算法"
        },
        "general": {
            "description": "通用类别，用于无法明确分类的信息",
            "examples": "天气很好、心情不错、随机想法"
        }
    }

    # 创建LLM分类函数
    llm_config = config["llm"]["config"]
    classifier = create_classifier_function(categories_config, default_category="general", llm_config=llm_config)
    
    # 测试对话
    test_messages = [
        {"role": "user", "content": "我叫张三，是一名Python工程师，喜欢喝咖啡"},
        {"role": "assistant", "content": "很高兴认识你张三"}
    ]
    
    logger.info("=" * 60)
    logger.info("🧠 增强Memory类测试")
    logger.info("=" * 60)

    # 方式一：使用add_with_classification
    logger.info("\n📋 方式一：使用add_with_classification")

    result = enhanced_memory.add_with_classification(
        test_messages,
        classifier,
        user_id="test_user"
    )

    # 方式二：一站式操作
    logger.info("\n📋 方式二：一站式操作")
    result2 = enhanced_memory.extract_and_classify_memories(
        test_messages,
        classifier,
        user_id="test_user2"
    )

    logger.info(f"\n✅ 测试完成！")
    logger.info(f"方式一结果: {len(result.get('results', []))} 条记忆")
    logger.info(f"方式二结果: {result2.get('stored_count', 0)} 条记忆")
