# Mem0 核心概念指南

本文档详细介绍Mem0的核心概念，包括`agent_id`、`run_id`的含义、常用接口参数以及情景记忆的概念和应用。

## 📋 目录

- [agent_id 和 run_id 详解](#agent_id-和-run_id-详解)
- [常用接口及参数说明](#常用接口及参数说明)
- [情景记忆概念](#情景记忆概念)
- [实际应用示例](#实际应用示例)
- [最佳实践](#最佳实践)

## 🔍 agent_id 和 run_id 详解

### 📋 agent_id（代理ID）

**定义**：用于标识特定的AI代理或助手

**作用**：
- 区分不同的代理，实现记忆的分层管理
- 为不同功能的AI助手提供独立的记忆空间
- 支持多代理系统的记忆隔离

**使用场景**：
```python
# 饮食助手
m.add("User prefers vegetarian food", 
      user_id="alice", 
      agent_id="diet-assistant")

# 电影推荐助手
m.add("User likes sci-fi movies", 
      user_id="alice", 
      agent_id="movie-assistant")
```

### 🔄 run_id（运行ID）

**定义**：用于标识特定的对话会话或任务执行

**作用**：
- 将一系列相关的消息或操作组织在一起
- 实现会话级别的记忆管理
- 支持任务导向的上下文保持

**使用场景**：
```python
# 特定咨询会话
m.add("User prefers vegetarian food", 
      user_id="alice", 
      agent_id="diet-assistant", 
      run_id="consultation-001")

# 群聊项目协作
m.add("Alice handles frontend development", 
      user_id="alice", 
      run_id="project-alpha-2024")
```

### 📊 记忆组织层次结构

```
用户 (user_id: "alice")
├── 代理1 (agent_id: "diet-assistant")
│   ├── 会话1 (run_id: "consultation-001")
│   │   ├── 记忆1: "喜欢素食"
│   │   └── 记忆2: "对坚果过敏"
│   └── 会话2 (run_id: "consultation-002")
│       └── 记忆3: "想尝试地中海饮食"
└── 代理2 (agent_id: "movie-assistant")
    ├── 会话1 (run_id: "session-123")
    │   └── 记忆4: "喜欢科幻电影"
    └── 会话2 (run_id: "session-456")
        └── 记忆5: "不喜欢恐怖片"
```

## 🛠️ 常用接口及参数说明

### 1. 添加记忆 (add)

```python
# 基础用法
m.add("I like pizza", user_id="alice")

# 带代理ID
m.add("I like pizza", user_id="alice", agent_id="food-assistant")

# 完整上下文
m.add("I like pizza", 
      user_id="alice", 
      agent_id="food-assistant", 
      run_id="session-123")

# 批量消息
messages = [
    {"role": "user", "content": "I prefer vegetarian food"},
    {"role": "assistant", "content": "Got it! I'll remember that."}
]
m.add(messages, 
      user_id="alice", 
      agent_id="diet-assistant", 
      run_id="consultation-001",
      metadata={"category": "dietary_preferences"})
```

**参数说明**：
- `messages`: 要存储的消息内容（字符串或消息列表）
- `user_id`: 用户标识符（必需）
- `agent_id`: 代理标识符（可选）
- `run_id`: 会话标识符（可选）
- `metadata`: 附加元数据（可选）
- `infer`: 是否推断记忆（默认True）

### 2. 搜索记忆 (search)

```python
# 用户级搜索
m.search("What do you know about me?", user_id="alice")

# 代理级搜索
m.search("What do you know about me?", 
         user_id="alice", 
         agent_id="diet-assistant")

# 会话级搜索
m.search("What do you know about me?", 
         user_id="alice", 
         run_id="consultation-001")

# 完整上下文搜索
m.search("What do you know about me?", 
         user_id="alice", 
         agent_id="diet-assistant", 
         run_id="consultation-001")
```

**参数说明**：
- `query`: 搜索查询（必需）
- `user_id`: 用户ID（可选，用于过滤）
- `agent_id`: 代理ID（可选，用于过滤）
- `run_id`: 会话ID（可选，用于过滤）
- `limit`: 返回结果数量限制（默认10）
- `filters`: 额外的过滤条件

### 3. 获取所有记忆 (get_all)

```python
# 获取用户所有记忆
all_user_memories = m.get_all(user_id="alice")

# 获取特定代理的记忆
agent_memories = m.get_all(user_id="alice", agent_id="diet-assistant")

# 获取特定会话的记忆
session_memories = m.get_all(user_id="alice", run_id="consultation-001")

# 获取特定代理和会话的记忆
specific_memories = m.get_all(
    user_id="alice", 
    agent_id="diet-assistant", 
    run_id="consultation-001"
)
```

### 4. 删除记忆 (delete/delete_all)

```python
# 删除特定记忆
m.delete(memory_id="memory-uuid")

# 删除用户所有记忆
m.delete_all(user_id="alice")

# 删除特定代理的记忆
m.delete_all(user_id="alice", agent_id="diet-assistant")

# 删除特定会话的记忆
m.delete_all(user_id="alice", run_id="consultation-001")
```

### 5. 更新记忆 (update)

```python
# 更新记忆内容
m.update(memory_id="memory-uuid", data="Updated content")
```

## 🧠 情景记忆概念

### 📋 什么是情景记忆（Episodic Memory）

**情景记忆**是Mem0中的一种重要记忆类型，它模拟人类大脑中存储特定事件和经历的记忆系统。在AI系统中，情景记忆专门用于存储和回忆**具体的对话片段、交互历史和特定情境下的经历**。

### 🎯 情景记忆的特点

#### 1. **时间和情境相关**
- 记录特定时间点的对话
- 保存具体的交互情境
- 维护事件的时间顺序

#### 2. **会话级别的记忆**
- 通过`run_id`组织特定会话
- 区分不同的对话场景
- 保持会话的连续性

#### 3. **短期到中期的存储**
- 比工作记忆持久，比语义记忆具体
- 可以跨会话保持，但通常有时效性
- 适合任务导向的记忆管理

### 📊 Mem0中的记忆类型层次

```
记忆类型层次结构：
├── 🔄 工作记忆 (Working Memory)
│   └── 当前会话的临时上下文
├── 📖 情景记忆 (Episodic Memory) ⭐
│   └── 特定对话和交互历史
├── 📚 事实记忆 (Factual Memory)
│   └── 结构化的知识和偏好
└── 🌐 语义记忆 (Semantic Memory)
    └── 概念和关系的理解
```

### 🔍 情景记忆 vs 其他记忆类型

| 记忆类型       | 持续时间 | 存储内容       | 使用场景     | 标识符     |
| -------------- | -------- | -------------- | ------------ | ---------- |
| **工作记忆**   | 当前会话 | 临时上下文     | 实时对话     | 无特殊标识 |
| **情景记忆** ⭐ | 会话级别 | 具体对话历史   | 任务导向交互 | `run_id`   |
| **事实记忆**   | 长期     | 用户偏好、知识 | 个性化服务   | `user_id`  |
| **语义记忆**   | 永久     | 概念和关系     | 知识理解     | 全局       |

## 🎯 实际应用示例

### 1. 多代理系统

```python
# 饮食助手
m.add("User prefers vegetarian food", 
      user_id="alice", 
      agent_id="diet-assistant", 
      run_id="consultation-001")

# 电影助手
m.add("User likes sci-fi movies", 
      user_id="alice", 
      agent_id="movie-assistant", 
      run_id="recommendation-session")

# 检索特定代理的记忆
diet_memories = m.search("food preferences", 
                        user_id="alice", 
                        agent_id="diet-assistant")
```

### 2. 群聊协作

```python
# 项目协作会话
project_run_id = "project-alpha-2024"

# 不同用户在同一项目中的记忆
m.add("Alice is responsible for frontend", 
      user_id="alice", 
      run_id=project_run_id)

m.add("Bob handles backend development", 
      user_id="bob", 
      run_id=project_run_id)

# 获取整个项目的记忆
project_memories = m.get_all(run_id=project_run_id)
```

### 3. 情景记忆应用

#### 任务导向的对话
```python
# 项目规划会话
project_messages = [
    {"role": "user", "content": "Let's plan the Q4 marketing strategy"},
    {"role": "assistant", "content": "Great! What are your main goals?"}
]
client.add(project_messages, 
          user_id="manager", 
          run_id="q4-marketing-planning")
```

#### 客户服务场景
```python
# 客服工单记忆
support_messages = [
    {"role": "user", "content": "My subscription isn't working"},
    {"role": "assistant", "content": "I can help with that. What specific issue?"},
    {"role": "user", "content": "Can't access premium features"}
]
client.add(support_messages, 
          user_id="customer123", 
          run_id="ticket-2024-001")
```

#### 教育和学习场景
```python
# 特定课程的学习记忆
lesson_messages = [
    {"role": "user", "content": "Can you explain algorithms?"},
    {"role": "assistant", "content": "Sure! Let me break it down step by step."}
]
client.add(lesson_messages,
          user_id="student789",
          run_id="algorithms-lesson-1")
```

## 💡 最佳实践

### 1. API参数优先级和组合规则

- **必需参数**：至少需要`user_id`、`agent_id`或`run_id`中的一个
- **组合使用**：可以同时使用多个ID进行精确定位
- **过滤层次**：`user_id` > `agent_id` > `run_id`（从宽到窄）
- **通配符支持**：某些API支持使用`"*"`作为通配符

### 2. 合理使用run_id

```python
# 为不同类型的交互使用不同的run_id
daily_planning = f"daily-plan-{date.today()}"
project_meeting = f"project-alpha-meeting-{session_id}"
customer_support = f"ticket-{ticket_number}"
```

### 3. 结合用户级记忆

```python
# 情景记忆 + 用户偏好
user_preferences = client.search("dietary preferences", user_id="alex")
session_context = client.search("restaurant discussion", 
                               user_id="alex", 
                               run_id="trip-planning-2024")
```

### 4. 适当的记忆生命周期

```python
# 可以设置过期时间
client.add(messages, 
          user_id="user123", 
          run_id="temp-session",
          metadata={"expires_at": "2024-12-31"})
```

## 🎨 情景记忆的优势

### 1. **上下文连续性**
- 保持特定任务的对话连贯性
- 避免重复解释背景信息
- 提供个性化的交互体验

### 2. **任务隔离**
- 不同任务的记忆相互独立
- 避免上下文混淆
- 支持并行的多任务处理

### 3. **时间感知**
- 记录事件的时间顺序
- 支持基于时间的记忆检索
- 可以追踪任务进展

## 🎯 总结

这种分层的记忆管理系统让你可以：

- 🎯 **精确控制**：根据不同维度组织和检索记忆
- 🔒 **隔离保护**：不同用户、代理、会话的记忆相互独立
- 📈 **扩展性强**：支持复杂的多用户、多代理场景
- 🔍 **灵活检索**：可以在不同粒度级别搜索记忆

情景记忆是Mem0中连接短期工作记忆和长期事实记忆的重要桥梁，通过`run_id`机制实现了任务导向的记忆管理、会话级别的上下文保持、具体交互历史的存储和个性化的用户体验。

这种设计让AI系统能够像人类一样，既记住具体的对话经历，又能在需要时准确回忆特定情境下的交互内容，从而提供更加智能和个性化的服务。

## 🔧 高级概念和技巧

### 1. 记忆过滤和搜索

#### 使用过滤器进行精确搜索
```python
# 使用复杂过滤器
filters = {
    "AND": [
        {"user_id": "alice"},
        {"agent_id": "diet-assistant"},
        {"metadata.category": "dietary_preferences"}
    ]
}
results = client.search("food preferences", version="v2", filters=filters)

# 通配符搜索
wildcard_filters = {
    "AND": [
        {"user_id": "alice"},
        {"run_id": "*"}  # 匹配所有run_id
    ]
}
all_user_memories = client.search("preferences", version="v2", filters=wildcard_filters)
```

#### 元数据增强搜索
```python
# 添加带元数据的记忆
client.add(messages,
          user_id="alice",
          metadata={
              "category": "health",
              "priority": "high",
              "tags": ["allergy", "dietary"]
          })

# 基于元数据搜索
metadata_search = client.search("allergies",
                               user_id="alice",
                               filters={"metadata.category": "health"})
```

### 2. 图记忆（Graph Memory）

#### 启用图记忆功能
```python
# 添加图记忆
client.add(messages,
          user_id="joseph",
          enable_graph=True,
          output_format="v1.1")

# 获取带关系的记忆
memories_with_relations = client.get_all(
    user_id="joseph",
    enable_graph=True,
    output_format="v1.1"
)

# 输出示例
{
  "results": [
    {"memory": "Name is Joseph", "categories": ["personal_details"]},
    {"memory": "Is from Seattle", "categories": ["personal_details"]},
    {"memory": "Is a software engineer", "categories": ["professional_details"]}
  ],
  "relations": [
    {"source": "joseph", "relationship": "name", "target": "joseph"},
    {"source": "joseph", "relationship": "city", "target": "seattle"},
    {"source": "joseph", "relationship": "job", "target": "software engineer"}
  ]
}
```

### 3. 记忆版本控制

#### 记忆历史追踪
```python
# 添加初始记忆
messages1 = [{"role": "user", "content": "I love meat dishes"}]
client.add(messages1, user_id="alex")

# 更新记忆
messages2 = [{"role": "user", "content": "I'm now vegetarian"}]
client.add(messages2, user_id="alex")

# 查看记忆变化历史
memory_id = "memory-uuid"
history = client.history(memory_id)
```

### 4. 异步记忆操作

#### 使用异步客户端
```python
from mem0 import AsyncMemoryClient

async def async_memory_operations():
    client = AsyncMemoryClient(api_key="your-api-key")

    # 异步添加记忆
    await client.add(messages, user_id="alice", agent_id="assistant")

    # 异步搜索
    results = await client.search("preferences", user_id="alice")

    # 异步获取所有记忆
    all_memories = await client.get_all(user_id="alice")

    # 异步删除
    await client.delete_all(user_id="alice")
```

## 📊 性能优化建议

### 1. 批量操作
```python
# 批量添加多个会话的记忆
sessions = [
    {"messages": session1_messages, "run_id": "session-001"},
    {"messages": session2_messages, "run_id": "session-002"},
    {"messages": session3_messages, "run_id": "session-003"}
]

for session in sessions:
    client.add(session["messages"],
              user_id="alice",
              run_id=session["run_id"])
```

### 2. 分页处理大量记忆
```python
# 分页获取大量记忆
page = 1
page_size = 50
all_memories = []

while True:
    memories = client.get_all(user_id="alice", page=page, page_size=page_size)
    if not memories.get("results"):
        break
    all_memories.extend(memories["results"])
    page += 1
```

### 3. 缓存策略
```python
# 实现简单的记忆缓存
memory_cache = {}

def get_cached_memories(user_id, agent_id=None):
    cache_key = f"{user_id}:{agent_id or 'all'}"

    if cache_key not in memory_cache:
        memory_cache[cache_key] = client.get_all(
            user_id=user_id,
            agent_id=agent_id
        )

    return memory_cache[cache_key]
```

## 🚨 常见问题和解决方案

### 1. 记忆重复问题
```python
# 使用哈希值避免重复记忆
import hashlib

def add_unique_memory(content, user_id, **kwargs):
    # 生成内容哈希
    content_hash = hashlib.md5(content.encode()).hexdigest()

    # 检查是否已存在
    existing = client.search(content, user_id=user_id, limit=1)
    if existing and existing["results"]:
        print("记忆已存在，跳过添加")
        return

    # 添加新记忆
    client.add(content, user_id=user_id,
              metadata={"hash": content_hash}, **kwargs)
```

### 2. 记忆过期管理
```python
from datetime import datetime, timedelta

# 添加带过期时间的记忆
expiry_date = datetime.now() + timedelta(days=30)
client.add(messages,
          user_id="alice",
          metadata={"expires_at": expiry_date.isoformat()})

# 清理过期记忆
def cleanup_expired_memories(user_id):
    memories = client.get_all(user_id=user_id)
    current_time = datetime.now()

    for memory in memories.get("results", []):
        expires_at = memory.get("metadata", {}).get("expires_at")
        if expires_at and datetime.fromisoformat(expires_at) < current_time:
            client.delete(memory["id"])
```

### 3. 记忆分类管理
```python
# 自动分类记忆
def categorize_and_add(content, user_id, **kwargs):
    # 简单的关键词分类
    categories = {
        "food": ["eat", "food", "restaurant", "meal"],
        "travel": ["trip", "travel", "vacation", "hotel"],
        "work": ["job", "work", "meeting", "project"]
    }

    detected_category = "general"
    for category, keywords in categories.items():
        if any(keyword in content.lower() for keyword in keywords):
            detected_category = category
            break

    client.add(content,
              user_id=user_id,
              metadata={"auto_category": detected_category},
              **kwargs)
```

## 🔗 相关资源

- [Mem0 官方文档](https://docs.mem0.ai/)
- [Mem0 GitHub 仓库](https://github.com/mem0ai/mem0)
- [Enhanced Memory System 项目](./README.md)
- [API 参考文档](https://docs.mem0.ai/api-reference)

## 📝 更新日志

- **v1.0** - 初始版本，包含基础概念和示例
- **v1.1** - 添加高级概念、性能优化和常见问题解决方案

---

*本文档基于Mem0官方文档和实际使用经验编写，持续更新中。*
