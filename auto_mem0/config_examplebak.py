#!/usr/bin/env python3
"""
配置示例文件
复制此文件为 config.py 并根据需要修改配置
"""

# 基础配置
CONFIG = {
    "vector_store": {
        "provider": "chroma",
        "config": {
            "collection_name": "enhanced_memory",
            "path": "./db/chroma_db"
        }
    },
    "llm": {
        "provider": "openai",
        "config": {
            "model": "gpt-3.5-turbo",  # 或者使用其他模型
            "temperature": 0.1,
            "api_key": "your-openai-api-key",  # 替换为你的API密钥
            "openai_base_url": "https://api.openai.com/v1"  # 或自定义URL
        }
    },
    "embedder": {
        "provider": "ollama",
        "config": {
            "model": "qwen3:8b",  # 或其他嵌入模型
            "ollama_base_url": "http://localhost:11434"  # Ollama服务地址
        }
    }
}

# 分类配置
CATEGORIES_CONFIG = {
    "personal_identity": {
        "description": "个人身份信息，包括姓名、年龄、职业、住址等基本信息",
        "examples": "我叫张三、今年25岁、是一名软件工程师、住在北京"
    },
    "personal_contact": {
        "description": "个人联系方式，包括电话、邮箱、微信、QQ等",
        "examples": "手机号是13800138000、邮箱是*****************、微信号是wechat123"
    },
    "preferences": {
        "description": "个人偏好和喜好，包括饮食偏好、娱乐爱好、生活习惯等",
        "examples": "喜欢喝咖啡、不喜欢辣食、爱听音乐、喜欢看电影"
    },
    "work_experience": {
        "description": "工作经历和职业相关信息，包括公司、项目、技能等",
        "examples": "在腾讯工作过、负责过AI项目、熟悉Python编程"
    },
    "education": {
        "description": "教育背景和学习经历，包括学校、专业、学历等",
        "examples": "毕业于清华大学、计算机科学专业、硕士学位"
    },
    "health_info": {
        "description": "健康相关信息，包括身体状况、医疗记录、运动习惯等",
        "examples": "有高血压、定期体检、每天跑步、对花粉过敏"
    },
    "family_social": {
        "description": "家庭和社交关系信息，包括家庭成员、朋友、社交活动等",
        "examples": "已婚有一个孩子、父母住在上海、朋友圈很广、经常参加聚会"
    },
    "financial": {
        "description": "财务相关信息，包括收入、支出、投资、理财等",
        "examples": "月收入2万、有房贷、投资股票、定期存款"
    },
    "travel_location": {
        "description": "旅行和地理位置信息，包括去过的地方、旅行计划等",
        "examples": "去过日本旅游、计划去欧洲、经常出差、住在市中心"
    },
    "news_topic": {
        "description": "新闻话题和时事关注，包括关注的新闻类型、话题偏好等",
        "examples": "关注科技新闻、喜欢财经资讯、对政治感兴趣"
    },
    "general": {
        "description": "通用类别，用于无法明确分类的信息或日常对话",
        "examples": "天气很好、心情不错、随机想法、一般性对话"
    }
}

# 服务器配置
SERVER_CONFIG = {
    "host": "0.0.0.0",
    "port": 8765,
    "debug": False,
    "log_level": "INFO"
}

# 日志配置
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s:%(lineno)d - %(levelname)s - %(message)s",
    "datefmt": "%Y-%m-%d %H:%M:%S"
}

# 性能配置
PERFORMANCE_CONFIG = {
    "max_memory_per_user": 1000,  # 每个用户最大记忆数量
    "embedding_cache_size": 10000,  # 嵌入向量缓存大小
    "batch_size": 10,  # 批处理大小
    "timeout": 30  # 请求超时时间（秒）
}

# 安全配置
SECURITY_CONFIG = {
    "enable_auth": False,  # 是否启用认证
    "api_key_required": False,  # 是否需要API密钥
    "rate_limit": {
        "enabled": False,
        "requests_per_minute": 60
    }
}

# 使用示例
if __name__ == "__main__":
    print("配置示例:")
    print(f"向量存储: {CONFIG['vector_store']['provider']}")
    print(f"LLM模型: {CONFIG['llm']['config']['model']}")
    print(f"嵌入模型: {CONFIG['embedder']['config']['model']}")
    print(f"分类数量: {len(CATEGORIES_CONFIG)}")
    print(f"服务器端口: {SERVER_CONFIG['port']}")
