#!/usr/bin/env python3
"""
系统功能测试脚本
用于验证Enhanced Memory System的基本功能
"""

import requests
import json
import time
import sys

def test_server_connection():
    """测试服务器连接"""
    print("🔗 测试服务器连接...")
    try:
        response = requests.get("http://localhost:8765/api/test", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器连接正常")
            return True
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务器正在运行")
        return False
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def test_memory_extraction():
    """测试记忆提取功能"""
    print("\n💭 测试记忆提取功能...")
    
    test_user_id = f"test_user_{int(time.time())}"
    test_message = "我叫测试用户，是一名AI工程师，喜欢研究机器学习"
    
    try:
        response = requests.post(
            "http://localhost:8765/api/chat",
            json={
                "user_id": test_user_id,
                "message": test_message,
                "extract_memory": True
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            stored_count = len(result.get("stored", []))
            print(f"✅ 记忆提取成功，存储了 {stored_count} 条记忆")
            
            # 显示存储的记忆
            for i, memory in enumerate(result.get("stored", []), 1):
                category = memory.get("category", "unknown")
                text = memory.get("memory", "")
                print(f"   记忆{i}: [{category}] {text}")
            
            return test_user_id, stored_count > 0
        else:
            print(f"❌ 记忆提取失败: {response.status_code}")
            return test_user_id, False
            
    except Exception as e:
        print(f"❌ 记忆提取测试失败: {e}")
        return test_user_id, False

def test_memory_retrieval(user_id):
    """测试记忆检索功能"""
    print("\n🔍 测试记忆检索功能...")
    
    try:
        response = requests.get(
            f"http://localhost:8765/api/memories?user_id={user_id}",
            timeout=10
        )
        
        if response.status_code == 200:
            memories = response.json()
            memory_count = len(memories.get("results", []))
            print(f"✅ 记忆检索成功，找到 {memory_count} 条记忆")
            
            # 显示检索到的记忆
            for i, memory in enumerate(memories.get("results", [])[:3], 1):
                category = memory.get("metadata", {}).get("category", "unknown")
                text = memory.get("memory", "")
                print(f"   记忆{i}: [{category}] {text}")
            
            return True
        else:
            print(f"❌ 记忆检索失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 记忆检索测试失败: {e}")
        return False

def test_update_event(user_id):
    """测试UPDATE事件处理"""
    print("\n🔄 测试UPDATE事件处理...")
    
    try:
        # 发送可能触发UPDATE的消息
        response = requests.post(
            "http://localhost:8765/api/chat",
            json={
                "user_id": user_id,
                "message": "我现在是一名高级AI工程师，专门研究深度学习",
                "extract_memory": True
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            stored_count = len(result.get("stored", []))
            print(f"✅ UPDATE事件处理成功，处理了 {stored_count} 条记忆")
            return True
        else:
            print(f"❌ UPDATE事件处理失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ UPDATE事件测试失败: {e}")
        return False

def test_memory_deletion(user_id):
    """测试记忆删除功能"""
    print("\n🗑️ 测试记忆删除功能...")
    
    try:
        response = requests.delete(
            f"http://localhost:8765/api/memories?user_id={user_id}",
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ 记忆删除成功")
            return True
        else:
            print(f"❌ 记忆删除失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 记忆删除测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 Enhanced Memory System 功能测试")
    print("=" * 50)
    
    # 测试结果统计
    tests = []
    
    # 1. 测试服务器连接
    server_ok = test_server_connection()
    tests.append(("服务器连接", server_ok))
    
    if not server_ok:
        print("\n❌ 服务器连接失败，无法继续测试")
        sys.exit(1)
    
    # 2. 测试记忆提取
    user_id, extraction_ok = test_memory_extraction()
    tests.append(("记忆提取", extraction_ok))
    
    # 3. 测试记忆检索
    if extraction_ok:
        retrieval_ok = test_memory_retrieval(user_id)
        tests.append(("记忆检索", retrieval_ok))
        
        # 4. 测试UPDATE事件
        update_ok = test_update_event(user_id)
        tests.append(("UPDATE事件", update_ok))
        
        # 5. 测试记忆删除
        deletion_ok = test_memory_deletion(user_id)
        tests.append(("记忆删除", deletion_ok))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} {status}")
        if result:
            passed += 1
    
    print("-" * 50)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常")
        sys.exit(0)
    else:
        print("⚠️ 部分测试失败，请检查系统配置")
        sys.exit(1)

if __name__ == "__main__":
    main()
