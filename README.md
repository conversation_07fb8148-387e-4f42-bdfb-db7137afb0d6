# Mem0 Test Project - 智能记忆管理系统

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

基于 Mem0 的增强记忆管理系统，提供智能分类、事件处理、多服务架构和Web界面交互。

## 🌟 项目特性

### 核心功能
- **🧠 智能记忆分类**：使用LLM自动对记忆进行多维度分类
- **⚡ 事件驱动处理**：支持ADD、UPDATE、DELETE、NONE等事件类型
- **🔍 向量存储检索**：基于ChromaDB/Qdrant的高效向量存储和相似性搜索
- **🌐 多服务架构**：提供OpenMemory API、Mem4LM Server、Web Demo等多种服务
- **📱 Web界面**：提供现代化的Web界面进行记忆管理和聊天交互
- **📊 实时监控**：详细的日志系统和SSE实时事件流

### 增强特性
- **🎯 分类优化**：UPDATE事件保持原有分类，避免重复分类开销
- **🔧 多模型支持**：支持OpenAI、Ollama等多种LLM和嵌入模型
- **⚙️ 灵活配置**：支持多种配置方式和自定义分类规则
- **📦 批量处理**：支持批量记忆提取和处理
- **🔐 权限控制**：基于用户和应用的访问控制机制

## 📁 项目结构

```
mem0_test/
├── auto_mem0/                          # 主项目目录
│   ├── enhanced_memory.py              # 增强记忆核心模块
│   ├── memory_categories.json          # 记忆分类配置
│   ├── requirements.txt                # 项目依赖
│   ├── web_demo/                       # Web演示服务
│   │   ├── simple_server.py           # 简化版Web服务器
│   │   └── static/                    # 静态资源文件
│   ├── mem0bak/                        # Mem0备份和扩展
│   │   └── openmemory/                # OpenMemory API服务
│   │       └── api/                   # API路由和模型
│   ├── mem4lm/                         # Mem4LM服务
│   │   ├── server/                    # 简化记忆服务器
│   │   └── openmemory/                # OpenMemory集成
│   ├── test/                           # 测试文件
│   └── db/                             # 数据库文件
└── README.md                           # 项目文档
```

## 🚀 快速开始

### 环境要求
- Python 3.8+
- 支持的LLM服务（OpenAI API 或 Ollama）
- 8GB+ RAM（推荐）

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd mem0_test
```

2. **安装依赖**
```bash
cd auto_mem0
pip install -r requirements.txt
```

3. **配置环境变量**
```bash
# 创建 .env 文件
cp config_example.py config.py

# 编辑配置文件，设置API密钥
export OPENAI_API_KEY="your-openai-api-key"
# 或者配置Ollama
export OLLAMA_BASE_URL="http://localhost:11434"
```

4. **启动服务**

选择以下任一服务启动：

#### Web演示服务（推荐新手）
```bash
cd web_demo
python simple_server.py
```
访问：http://localhost:8765

#### OpenMemory完整API服务
```bash
cd mem0bak/openmemory/api
python main.py
```
访问：http://localhost:8000

#### Mem4LM简化服务
```bash
cd mem4lm/server
python main.py
```
访问：http://localhost:8000

## 🔧 配置说明

### 基础配置
```python
config = {
    "llm": {
        "provider": "openai",  # 或 "ollama"
        "config": {
            "model": "gpt-4o-mini",
            "temperature": 0.1,
            "api_key": "your-api-key"
        }
    },
    "embedder": {
        "provider": "openai",  # 或 "ollama"
        "config": {
            "model": "text-embedding-3-small",
            "api_key": "your-api-key"
        }
    },
    "vector_store": {
        "provider": "chroma",  # 或 "qdrant"
        "config": {
            "collection_name": "memories",
            "path": "./db/chroma"
        }
    }
}
```

### 记忆分类配置
编辑 `memory_categories.json` 文件来自定义分类规则：
```json
{
  "personal_identity": {
    "description": "个人身份信息，包括姓名、年龄、性别、职业等",
    "examples": "姓名是张三、今年25岁、软件工程师"
  },
  "food": {
    "description": "美食偏好，包括喜欢的菜系、口味、食物类型等",
    "examples": "喜欢川菜、爱吃甜食、偏爱海鲜"
  }
}
```

## 📚 API文档

### Web Demo Server (端口: 8765)

#### 聊天接口
- `POST /api/chat` - 普通聊天
- `POST /api/chat/stream` - 流式聊天
- `POST /api/chat/reset` - 重置聊天历史

#### 记忆管理
- `GET /api/memories` - 获取记忆列表
- `DELETE /api/memories` - 清空用户记忆
- `POST /api/memories/delete` - 删除特定记忆
- `POST /api/extract-memories` - 批量提取记忆

#### 实时通信
- `GET /events` - SSE事件流

### OpenMemory API (端口: 8000)

#### 记忆管理 (`/api/v1/memories`)
- `GET /` - 获取记忆列表（支持分页、过滤、排序）
- `POST /` - 创建新记忆
- `GET /{memory_id}` - 获取单个记忆
- `PUT /{memory_id}` - 更新记忆
- `DELETE /` - 批量删除记忆
- `POST /filter` - 高级过滤查询
- `GET /{memory_id}/related` - 获取相关记忆

#### 应用管理 (`/api/v1/apps`)
- `GET /` - 获取应用列表
- `GET /{app_id}` - 获取应用详情
- `PUT /{app_id}` - 更新应用状态

#### 配置管理 (`/api/v1/config`)
- `GET /` - 获取完整配置
- `PUT /` - 更新配置
- `POST /reset` - 重置为默认配置

### Mem4LM Server (端口: 8000)

#### 核心接口
- `POST /memories` - 创建记忆
- `GET /memories` - 获取记忆列表
- `POST /search` - 搜索记忆
- `DELETE /memories/{memory_id}` - 删除记忆
- `POST /reset` - 重置所有记忆

详细API文档请访问各服务的 `/docs` 端点查看Swagger文档。

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
python -m pytest test/

# 运行特定测试
python test_system.py
python test_llm.py
```

### 手动测试
```bash
# 测试记忆系统
python -c "
from enhanced_memory import EnhancedMemory
memory = EnhancedMemory.from_config('config.py')
result = memory.add('我喜欢吃川菜', user_id='test_user')
print(result)
"
```

## 🔍 使用示例

### 基础记忆操作
```python
from enhanced_memory import EnhancedMemory

# 初始化记忆系统
memory = EnhancedMemory.from_config("config.py")

# 添加记忆
result = memory.add("我叫张三，是一名软件工程师", user_id="user123")

# 搜索记忆
memories = memory.search("张三的职业", user_id="user123")

# 获取所有记忆
all_memories = memory.get_all(user_id="user123")
```

### Web界面使用
1. 启动Web服务：`python web_demo/simple_server.py`
2. 打开浏览器访问：http://localhost:8765
3. 在聊天界面输入消息，系统会自动提取和分类记忆
4. 在记忆管理页面查看、搜索和管理记忆

## 🛠️ 开发指南

### 添加新的记忆分类
1. 编辑 `memory_categories.json`
2. 添加新的分类定义和示例
3. 重启服务使配置生效

### 扩展API功能
1. 在相应的路由文件中添加新端点
2. 更新数据模型（如需要）
3. 添加相应的测试用例

### 自定义LLM提供商
1. 实现LLM接口
2. 在配置中注册新提供商
3. 更新配置文档

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 常见问题

### Q: UPDATE事件为什么不重新分类？
A: 为了节省LLM调用成本和保持分类一致性，UPDATE事件只更新内容和向量，保持原有分类。

### Q: NONE事件为什么什么都不做？
A: NONE事件表示无需记忆的内容（如"好的"、"知道了"等确认回复），系统会完全跳过处理以节省计算资源。

### Q: 如何切换不同的向量数据库？
A: 在配置文件中修改 `vector_store.provider` 为 "chroma" 或 "qdrant"，并相应配置连接参数。

### Q: 支持哪些LLM模型？
A: 支持OpenAI的所有模型（GPT-4、GPT-3.5等）和Ollama本地模型（Llama2、Mistral等）。

## 📞 支持

如有问题或建议，请：
- 提交 [Issue](https://github.com/your-repo/issues)
- 发送邮件至：<EMAIL>
- 查看 [Wiki](https://github.com/your-repo/wiki) 获取更多文档

---

⭐ 如果这个项目对你有帮助，请给它一个星标！
