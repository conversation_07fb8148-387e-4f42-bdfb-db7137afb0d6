\API 接口文档
概述
Simple Server 是一个基于 FastAPI 的记忆增强、记忆管理。
基础接口
1. 健康检查
GET /healthz


描述: 健康检查接口
响应:
{
  "status": "ok"
}


记忆管理接口
1. 获取记忆列表
GET /api/memories


描述: 获取指定用户的记忆列表
查询参数:
user_id (string, 必需): 用户ID
limit (int, 可选): 返回记忆数量限制，默认200
响应:
{
  "results": [
    {
      "id": "memory_id",
      "memory": "记忆内容",
      "user_id": "用户ID",
      "metadata": {
        "category": "分类名称"
      },
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}


------------
2. 清空用户记忆
DELETE /api/memories


描述: 清空指定用户的所有记忆
查询参数:
user_id (string, 必需): 用户ID
响应:
{
  "ok": true
}


错误响应:
{
  "ok": false,
  "error": "错误信息"
}


------------
3. 删除指定记忆
POST /api/memories/delete


描述: 删除指定的记忆条目
请求体:
{
  "memory_id": "要删除的记忆ID"
}


响应:
{
  "ok": true
}


错误响应:
{
  "ok": false,
  "error": "错误信息"
}



记忆提取接口
批量提取记忆
POST /api/extract-memories


描述: 从用户的聊天历史中批量提取记忆
请求体:
{
  "user_id": "用户ID",
  "messages": [
    {"role": "user", "content": "My subscription isn't working"},
    {"role": "assistant", "content": "I can help with that. What specific issue are you experiencing?"},
    {"role": "user", "content": "I can't access premium features even though I paid"}
]
}


响应:
{
  "extracted_count": 5,
  "message": "成功提取了 5 条记忆"
}


错误响应:
{
  "extracted_count": 0,
  "error": "错误信息"
}


错误处理
所有接口在发生错误时都会返回适当的HTTP状态码和错误信息。
常见错误响应格式:
{
  "ok": false,
  "error": "具体错误信息"
}

